"""
إعداد نظام شؤون الموظفين
Employee Management System Setup
"""

from setuptools import setup, find_packages
import os

# قراءة محتوى README
def read_readme():
    with open("README.md", "r", encoding="utf-8") as fh:
        return fh.read()

# قراءة المتطلبات
def read_requirements():
    with open("requirements.txt", "r", encoding="utf-8") as fh:
        return [line.strip() for line in fh if line.strip() and not line.startswith("#")]

setup(
    name="employee-management-system",
    version="1.0.0",
    author="نظام شؤون الموظفين",
    author_email="<EMAIL>",
    description="نظام شامل لإدارة شؤون الموظفين مع دعم اللغة العربية",
    long_description=read_readme(),
    long_description_content_type="text/markdown",
    url="https://github.com/company/employee-management-system",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 5 - Production/Stable",
        "Intended Audience :: End Users/Desktop",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Framework :: Flask",
        "Topic :: Office/Business :: Financial :: Accounting",
        "Topic :: Office/Business :: Scheduling",
        "Natural Language :: Arabic",
    ],
    python_requires=">=3.8",
    install_requires=read_requirements(),
    extras_require={
        "dev": [
            "pytest>=7.4.2",
            "pytest-flask>=1.2.0",
            "pytest-cov>=4.1.0",
            "flake8>=6.1.0",
            "black>=23.9.1",
        ],
        "redis": [
            "redis>=4.6.0",
        ],
        "postgresql": [
            "psycopg2-binary>=2.9.7",
        ],
        "mysql": [
            "PyMySQL>=1.1.0",
        ],
    },
    entry_points={
        "console_scripts": [
            "employee-system=app:main",
            "employee-migrate=migrate_database:main",
            "employee-test=run_tests:main",
        ],
    },
    include_package_data=True,
    package_data={
        "": [
            "templates/*.html",
            "templates/**/*.html",
            "static/css/*.css",
            "static/js/*.js",
            "static/images/*",
            "*.md",
            "*.txt",
            "*.json",
        ],
    },
    zip_safe=False,
    keywords=[
        "employee management",
        "hr system",
        "attendance tracking",
        "leave management",
        "arabic support",
        "flask application",
        "إدارة الموظفين",
        "نظام الموارد البشرية",
        "تتبع الحضور",
        "إدارة الإجازات",
    ],
    project_urls={
        "Bug Reports": "https://github.com/company/employee-management-system/issues",
        "Source": "https://github.com/company/employee-management-system",
        "Documentation": "https://github.com/company/employee-management-system/wiki",
    },
)
