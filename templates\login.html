<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #4a90e2; /* A modern blue */
            --secondary-color: #5c6b73; /* Darker grey for secondary elements */
            --accent-color: #f5a623; /* Orange for accents */
            --background-light: #f4f7f6; /* Light background for the body */
            --surface-card: #ffffff; /* White for cards */
            --text-dark: #333333;
            --text-light: #ffffff;
            --border-color: #e0e0e0;
            --shadow-light: rgba(0, 0, 0, 0.08);
            --success-color: #5cb85c;
            --danger-color: #d9534f;
            --warning-color: #f0ad4e;
            --info-color: #5bc0de;
        }

        body {
            font-family: 'Roboto', sans-serif;
            background-color: var(--background-light);
            color: var(--text-dark);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        .login-container {
            background-color: var(--surface-card);
            border-radius: 10px;
            box-shadow: 0 4px 12px var(--shadow-light);
            padding: 2.5rem;
            width: 100%;
            max-width: 450px;
            text-align: center;
            animation: fadeIn 0.8s ease-out;
        }
        .login-container h2 {
            color: var(--primary-color);
            font-size: 2.2rem;
            margin-bottom: 2rem;
            font-weight: 700;
        }
        .form-group {
            margin-bottom: 1.5rem;
            text-align: right;
        }
        .form-group label {
            display: block;
            margin-bottom: 0.6rem;
            color: var(--text-dark);
            font-weight: 500;
            font-size: 1rem;
        }
        .form-control {
            border-radius: 8px;
            border: 1px solid var(--border-color);
            padding: 0.8rem 1.2rem;
            font-size: 1rem;
            transition: all 0.3s ease;
        }
        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.25rem rgba(74, 144, 226, 0.25);
        }
        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            border-radius: 8px;
            padding: 0.9rem 1.5rem;
            font-size: 1.1rem;
            font-weight: 600;
            color: var(--text-light);
            width: 100%;
            margin-top: 2rem;
            transition: all 0.3s ease;
        }
        .btn-primary:hover {
            background-color: #3a7bd5;
            border-color: #3a7bd5;
            transform: translateY(-2px);
        }
        .alert {
            border-radius: 8px;
            margin-bottom: 1.5rem;
            font-size: 0.95rem;
            text-align: right;
        }
        .mt-3 a {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 600;
            transition: color 0.3s ease;
        }
        .mt-3 a:hover {
            color: #3a7bd5;
        }
        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        @media (max-width: 576px) {
            .login-container {
                padding: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <h2>تسجيل الدخول</h2>
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}
        <form method="POST" action="{{ url_for('main.login') }}">
            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
            <div class="form-group">
                        <label for="username">اسم المستخدم</label>
                <input type="text" class="form-control" id="username" name="username" required>
            </div>
            <div class="form-group">
                        <label for="password">كلمة المرور</label>
                <input type="password" class="form-control" id="password" name="password" required>
            </div>
            <button type="submit" class="btn btn-primary">تسجيل الدخول</button>
        </form>
        <p class="mt-3">ليس لديك حساب؟ <a href="{{ url_for('main.register') }}">سجل هنا</a></p>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>