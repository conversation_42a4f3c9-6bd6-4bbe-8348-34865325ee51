# استخدام Python 3.11 كصورة أساسية
FROM python:3.11-slim

# تعيين متغيرات البيئة
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1
ENV FLASK_APP=app.py
ENV FLASK_ENV=production

# إنشاء مجلد العمل
WORKDIR /app

# تثبيت متطلبات النظام
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    libffi-dev \
    libssl-dev \
    && rm -rf /var/lib/apt/lists/*

# نسخ ملف المتطلبات وتثبيتها
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# نسخ ملفات التطبيق
COPY . .

# إنشاء مجلدات الرفع
RUN mkdir -p static/uploads/photos static/uploads/documents

# تعيين الصلاحيات
RUN chmod +x migrate_database.py run_tests.py

# إنشاء مستخدم غير جذر
RUN adduser --disabled-password --gecos '' appuser && \
    chown -R appuser:appuser /app
USER appuser

# تعريض المنفذ
EXPOSE 5000

# تشغيل ترحيل قاعدة البيانات عند البدء
RUN python migrate_database.py

# الأمر الافتراضي لتشغيل التطبيق
CMD ["python", "app.py"]
