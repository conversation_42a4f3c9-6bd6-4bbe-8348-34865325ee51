# نظام إدارة شؤون الموظفين
## Employee Management System

نظام شامل لإدارة شؤون الموظفين مبني بـ Flask مع دعم كامل للغة العربية وتصميم RTL.

## المميزات الرئيسية

### 🔐 الأمان والحماية
- حماية من هجمات CSRF
- تشفير كلمات المرور
- جلسات آمنة مع انتهاء صلاحية
- التحقق من صحة البيانات المدخلة
- حماية من هجمات XSS

### 👥 إدارة الموظفين
- إضافة وتعديل وحذف الموظفين
- رفع الصور الشخصية والوثائق
- تتبع تاريخ التوظيف وسنوات الخدمة
- حساب العمر تلقائياً
- إدارة الأقسام والمناصب

### 📊 الحضور والغياب
- تسجيل الحضور اليومي
- تتبع أوقات الدخول والخروج
- إحصائيات الحضور الشهرية
- تقارير الغياب والتأخير

### 🏖️ إدارة الإجازات
- طلب الإجازات السنوية والمرضية
- موافقة أو رفض طلبات الإجازة
- تتبع رصيد الإجازات
- تقارير الإجازات المستخدمة

### 📄 إدارة الوثائق
- رفع وتنظيم وثائق الموظفين
- أنواع مختلفة من الوثائق
- معاينة وتحميل الملفات
- أمان في رفع الملفات

### 🚪 تسجيل المغادرة
- تسجيل المغادرة المؤقتة
- تحديد سبب المغادرة
- تتبع أوقات العودة
- موافقة المشرفين

### 📈 التقارير والإحصائيات
- تقارير شاملة للموظفين
- إحصائيات الحضور والغياب
- تقارير الإجازات والمغادرة
- تصدير التقارير إلى Excel
- رسوم بيانية تفاعلية

### 🔍 البحث المتقدم
- البحث في جميع بيانات الموظفين
- فلترة متقدمة حسب القسم والمنصب
- البحث بالتاريخ والراتب
- اقتراحات البحث الذكية

### 👑 نظام الأذونات
- أدوار مختلفة للمستخدمين
- صلاحيات محددة لكل دور
- حماية الصفحات الحساسة
- إدارة مرنة للأذونات

## التقنيات المستخدمة

- **Backend**: Flask (Python)
- **Database**: SQLite مع SQLAlchemy ORM
- **Frontend**: HTML5, CSS3, JavaScript, Bootstrap 5
- **Authentication**: Flask-Login
- **Forms**: Flask-WTF
- **Caching**: Redis/Flask-Caching
- **Reports**: Pandas, Matplotlib
- **Testing**: unittest, coverage

## متطلبات النظام

- Python 3.8+
- Redis (اختياري للتخزين المؤقت)
- متصفح حديث يدعم CSS Grid و Flexbox

## التثبيت والتشغيل

### 1. تحميل المشروع
```bash
git clone [repository-url]
cd employee-management-system
```

### 2. إنشاء بيئة افتراضية
```bash
python -m venv venv
source venv/bin/activate  # Linux/Mac
# أو
venv\Scripts\activate     # Windows
```

### 3. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

### 4. إعداد قاعدة البيانات
```bash
python migrate_database.py
```

### 5. تشغيل التطبيق
```bash
python app.py
```

التطبيق سيعمل على: `http://localhost:5000`

## تشغيل الاختبارات

### تشغيل جميع الاختبارات
```bash
python run_tests.py all
```

### تشغيل اختبارات محددة
```bash
python run_tests.py models      # اختبارات النماذج
python run_tests.py validators  # اختبارات التحقق
python run_tests.py utils       # اختبارات الدوال المساعدة
python run_tests.py files       # اختبارات الملفات
python run_tests.py database    # اختبارات قاعدة البيانات
```

### إنشاء تقرير الاختبارات
```bash
python run_tests.py report
```

## هيكل المشروع

```
employee-management-system/
├── app.py                      # الملف الرئيسي
├── app_init.py                 # إعداد التطبيق
├── models.py                   # نماذج قاعدة البيانات
├── routes.py                   # المسارات والعرض
├── app_functions.py            # دوال التطبيق الأساسية
├── validators.py               # دوال التحقق من صحة البيانات
├── utils.py                    # دوال مساعدة
├── error_handlers.py           # معالجة الأخطاء
├── permissions.py              # نظام الأذونات
├── reports.py                  # نظام التقارير
├── advanced_search.py          # البحث المتقدم
├── performance_optimizer.py    # تحسين الأداء
├── migrate_database.py         # ترحيل قاعدة البيانات
├── run_tests.py               # تشغيل الاختبارات
├── requirements.txt           # متطلبات المشروع
├── static/                    # الملفات الثابتة
│   ├── css/
│   ├── js/
│   └── uploads/
├── templates/                 # قوالب HTML
│   ├── base.html
│   ├── enhanced_*.html
│   └── errors/
└── tests/                     # ملفات الاختبار
    ├── test_models.py
    └── test_functions.py
```

## الإعدادات

### متغيرات البيئة
```bash
export SECRET_KEY="your-secret-key-here"
export DATABASE_URL="sqlite:///employees.db"
export REDIS_URL="redis://localhost:6379/0"
export FLASK_ENV="development"  # أو "production"
```

### إعدادات قاعدة البيانات
- يمكن تغيير نوع قاعدة البيانات من SQLite إلى PostgreSQL أو MySQL
- تحديث `DATABASE_URL` في متغيرات البيئة

## الأمان

### أفضل الممارسات المطبقة
- تشفير كلمات المرور باستخدام bcrypt
- حماية من هجمات CSRF
- تنظيف المدخلات من XSS
- جلسات آمنة مع انتهاء صلاحية
- التحقق من صحة الملفات المرفوعة
- صلاحيات محددة للمستخدمين

### نصائح الأمان
- استخدم كلمة مرور قوية للـ SECRET_KEY
- فعل HTTPS في الإنتاج
- قم بتحديث المكتبات بانتظام
- راجع سجلات النظام دورياً

## الأداء

### التحسينات المطبقة
- فهرسة قاعدة البيانات
- التخزين المؤقت للاستعلامات
- ضغط الملفات الثابتة
- تحسين الاستعلامات
- مراقبة الأداء

### نصائح التحسين
- استخدم Redis للتخزين المؤقت
- فعل ضغط gzip
- استخدم CDN للملفات الثابتة
- راقب استهلاك الذاكرة

## المساهمة

نرحب بالمساهمات! يرجى:

1. عمل Fork للمشروع
2. إنشاء branch جديد للميزة
3. كتابة اختبارات للكود الجديد
4. التأكد من نجاح جميع الاختبارات
5. إرسال Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف LICENSE للتفاصيل.

## الدعم

للحصول على الدعم:
- افتح issue في GitHub
- راجع الوثائق
- تحقق من الاختبارات للأمثلة

## التحديثات المستقبلية

- [ ] دعم قواعد بيانات متعددة
- [ ] API RESTful
- [ ] تطبيق موبايل
- [ ] إشعارات فورية
- [ ] تكامل مع أنظمة خارجية
- [ ] لوحة تحكم متقدمة
- [ ] تقارير أكثر تفصيلاً
