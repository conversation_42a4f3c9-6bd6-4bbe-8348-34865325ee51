"""
نظام تحسين الأداء والتحسينات التقنية
"""

from functools import wraps
from flask import request, g, current_app
from flask_caching import Cache
from sqlalchemy import event, text
from sqlalchemy.engine import Engine
from app_init import db
import time
import logging
from datetime import datetime, timedelta
import redis
import json
from collections import defaultdict

# إعداد التخزين المؤقت
cache = Cache()

# إعداد Redis للتخزين المؤقت المتقدم
try:
    redis_client = redis.Redis(host='localhost', port=6379, db=0, decode_responses=True)
    redis_available = True
except:
    redis_available = False
    redis_client = None

# إعداد logging للأداء
performance_logger = logging.getLogger('performance')
performance_logger.setLevel(logging.INFO)

class PerformanceMonitor:
    """مراقب الأداء"""
    
    def __init__(self):
        self.query_times = []
        self.slow_queries = []
        self.request_times = defaultdict(list)
        self.cache_stats = {'hits': 0, 'misses': 0}
    
    def log_query_time(self, query, duration):
        """تسجيل وقت الاستعلام"""
        self.query_times.append(duration)
        
        # تسجيل الاستعلامات البطيئة (أكثر من 100ms)
        if duration > 0.1:
            self.slow_queries.append({
                'query': str(query),
                'duration': duration,
                'timestamp': datetime.now()
            })
            performance_logger.warning(f"Slow query detected: {duration:.3f}s - {query}")
    
    def log_request_time(self, endpoint, duration):
        """تسجيل وقت الطلب"""
        self.request_times[endpoint].append(duration)
        
        # تسجيل الطلبات البطيئة (أكثر من 1 ثانية)
        if duration > 1.0:
            performance_logger.warning(f"Slow request: {endpoint} - {duration:.3f}s")
    
    def get_performance_stats(self):
        """الحصول على إحصائيات الأداء"""
        avg_query_time = sum(self.query_times) / len(self.query_times) if self.query_times else 0
        
        request_stats = {}
        for endpoint, times in self.request_times.items():
            request_stats[endpoint] = {
                'avg_time': sum(times) / len(times),
                'max_time': max(times),
                'min_time': min(times),
                'count': len(times)
            }
        
        return {
            'avg_query_time': avg_query_time,
            'slow_queries_count': len(self.slow_queries),
            'recent_slow_queries': self.slow_queries[-10:],
            'request_stats': request_stats,
            'cache_stats': self.cache_stats
        }

# إنشاء مثيل من مراقب الأداء
performance_monitor = PerformanceMonitor()

# مراقبة استعلامات قاعدة البيانات
@event.listens_for(Engine, "before_cursor_execute")
def receive_before_cursor_execute(conn, cursor, statement, parameters, context, executemany):
    context._query_start_time = time.time()

@event.listens_for(Engine, "after_cursor_execute")
def receive_after_cursor_execute(conn, cursor, statement, parameters, context, executemany):
    total = time.time() - context._query_start_time
    performance_monitor.log_query_time(statement, total)

def monitor_request_time(f):
    """ديكوريتر لمراقبة وقت الطلبات"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        start_time = time.time()
        result = f(*args, **kwargs)
        duration = time.time() - start_time
        performance_monitor.log_request_time(request.endpoint, duration)
        return result
    return decorated_function

class DatabaseOptimizer:
    """محسن قاعدة البيانات"""
    
    @staticmethod
    def optimize_queries():
        """تحسين الاستعلامات الشائعة"""
        # إنشاء فهارس إضافية للاستعلامات الشائعة
        indexes_to_create = [
            "CREATE INDEX IF NOT EXISTS idx_employee_department_active ON employees(department_id, is_active)",
            "CREATE INDEX IF NOT EXISTS idx_attendance_employee_date ON attendance(employee_id, date)",
            "CREATE INDEX IF NOT EXISTS idx_leave_employee_status ON leaves(employee_id, status)",
            "CREATE INDEX IF NOT EXISTS idx_document_employee_type ON documents(employee_id, document_type)",
            "CREATE INDEX IF NOT EXISTS idx_departure_employee_date ON departures(employee_id, departure_date)"
        ]
        
        for index_sql in indexes_to_create:
            try:
                db.session.execute(text(index_sql))
                db.session.commit()
            except Exception as e:
                db.session.rollback()
                current_app.logger.error(f"Error creating index: {e}")
    
    @staticmethod
    def analyze_database():
        """تحليل قاعدة البيانات"""
        try:
            db.session.execute(text("ANALYZE"))
            db.session.commit()
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Error analyzing database: {e}")
    
    @staticmethod
    def vacuum_database():
        """تنظيف قاعدة البيانات"""
        try:
            db.session.execute(text("VACUUM"))
            db.session.commit()
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Error vacuuming database: {e}")

class CacheManager:
    """مدير التخزين المؤقت"""
    
    def __init__(self):
        self.default_timeout = 300  # 5 دقائق
    
    def get_cached_data(self, key):
        """الحصول على البيانات المخزنة مؤقتاً"""
        try:
            if redis_available:
                data = redis_client.get(key)
                if data:
                    performance_monitor.cache_stats['hits'] += 1
                    return json.loads(data)
            else:
                data = cache.get(key)
                if data:
                    performance_monitor.cache_stats['hits'] += 1
                    return data
        except Exception as e:
            current_app.logger.error(f"Cache get error: {e}")
        
        performance_monitor.cache_stats['misses'] += 1
        return None
    
    def set_cached_data(self, key, data, timeout=None):
        """تخزين البيانات مؤقتاً"""
        if timeout is None:
            timeout = self.default_timeout
        
        try:
            if redis_available:
                redis_client.setex(key, timeout, json.dumps(data, default=str))
            else:
                cache.set(key, data, timeout=timeout)
        except Exception as e:
            current_app.logger.error(f"Cache set error: {e}")
    
    def delete_cached_data(self, key):
        """حذف البيانات المخزنة مؤقتاً"""
        try:
            if redis_available:
                redis_client.delete(key)
            else:
                cache.delete(key)
        except Exception as e:
            current_app.logger.error(f"Cache delete error: {e}")
    
    def clear_cache_pattern(self, pattern):
        """حذف البيانات المخزنة مؤقتاً بنمط معين"""
        try:
            if redis_available:
                keys = redis_client.keys(pattern)
                if keys:
                    redis_client.delete(*keys)
        except Exception as e:
            current_app.logger.error(f"Cache pattern delete error: {e}")

# إنشاء مثيل من مدير التخزين المؤقت
cache_manager = CacheManager()

def cached_query(key_prefix, timeout=300):
    """ديكوريتر للتخزين المؤقت للاستعلامات"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            # إنشاء مفتاح فريد للتخزين المؤقت
            cache_key = f"{key_prefix}:{hash(str(args) + str(kwargs))}"
            
            # محاولة الحصول على البيانات من التخزين المؤقت
            cached_result = cache_manager.get_cached_data(cache_key)
            if cached_result is not None:
                return cached_result
            
            # تنفيذ الدالة وتخزين النتيجة
            result = f(*args, **kwargs)
            cache_manager.set_cached_data(cache_key, result, timeout)
            
            return result
        return decorated_function
    return decorator

def invalidate_cache_on_change(cache_patterns):
    """ديكوريتر لحذف التخزين المؤقت عند التغيير"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            result = f(*args, **kwargs)
            
            # حذف البيانات المخزنة مؤقتاً
            for pattern in cache_patterns:
                cache_manager.clear_cache_pattern(pattern)
            
            return result
        return decorated_function
    return decorator

class QueryOptimizer:
    """محسن الاستعلامات"""
    
    @staticmethod
    def get_employees_with_departments():
        """الحصول على الموظفين مع الأقسام بطريقة محسنة"""
        from models import Employee, Department
        
        return db.session.query(Employee, Department).join(
            Department, Employee.department_id == Department.id, isouter=True
        ).options(
            db.joinedload(Employee.department)
        ).all()
    
    @staticmethod
    def get_attendance_summary(employee_id, start_date, end_date):
        """الحصول على ملخص الحضور بطريقة محسنة"""
        from models import Attendance
        from sqlalchemy import func
        
        return db.session.query(
            Attendance.status,
            func.count(Attendance.id).label('count')
        ).filter(
            Attendance.employee_id == employee_id,
            Attendance.date.between(start_date, end_date)
        ).group_by(Attendance.status).all()
    
    @staticmethod
    def get_department_statistics():
        """الحصول على إحصائيات الأقسام بطريقة محسنة"""
        from models import Employee, Department
        from sqlalchemy import func
        
        return db.session.query(
            Department.name,
            func.count(Employee.id).label('employee_count'),
            func.avg(Employee.salary).label('avg_salary')
        ).outerjoin(Employee).group_by(Department.id, Department.name).all()

def init_performance_monitoring(app):
    """تهيئة مراقبة الأداء"""
    cache.init_app(app)
    
    @app.before_request
    def before_request():
        g.start_time = time.time()
    
    @app.after_request
    def after_request(response):
        if hasattr(g, 'start_time'):
            duration = time.time() - g.start_time
            performance_monitor.log_request_time(request.endpoint or 'unknown', duration)
        return response
    
    # تسجيل دوال التحسين في Jinja2
    app.jinja_env.globals['performance_stats'] = performance_monitor.get_performance_stats

def optimize_database_on_startup():
    """تحسين قاعدة البيانات عند بدء التشغيل"""
    try:
        DatabaseOptimizer.optimize_queries()
        DatabaseOptimizer.analyze_database()
        current_app.logger.info("Database optimization completed")
    except Exception as e:
        current_app.logger.error(f"Database optimization failed: {e}")

# دوال مساعدة للتحسين
def batch_insert(model_class, data_list, batch_size=100):
    """إدراج مجموعي للبيانات"""
    for i in range(0, len(data_list), batch_size):
        batch = data_list[i:i + batch_size]
        db.session.bulk_insert_mappings(model_class, batch)
    db.session.commit()

def batch_update(model_class, data_list, batch_size=100):
    """تحديث مجموعي للبيانات"""
    for i in range(0, len(data_list), batch_size):
        batch = data_list[i:i + batch_size]
        db.session.bulk_update_mappings(model_class, batch)
    db.session.commit()

# إنشاء مثيل من محسن الاستعلامات
query_optimizer = QueryOptimizer()
