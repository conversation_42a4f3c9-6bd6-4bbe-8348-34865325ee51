<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}نظام شؤون الموظفين{% endblock %}</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <!-- Enhanced Custom CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/enhanced-style.css') }}">
    <style>
        :root {
            --primary-color: #007bff;
            --secondary-color: #6c757d;
            --light-bg: #f8f9fa;
            --dark-bg: #343a40;
            --card-shadow: rgba(0, 0, 0, 0.1);
            --card-hover-shadow: rgba(0, 0, 0, 0.15);
        }

        body {
            font-family: 'Arial', sans-serif;
            background-color: var(--light-bg);
            color: #333;
        }
        .navbar {
            background-color: var(--dark-bg) !important;
        }
        .sidebar {
            height: 100vh;
            background-color: var(--dark-bg);
            padding-top: 20px;
            color: white;
            box-shadow: 2px 0 5px var(--card-shadow);
        }
        .sidebar .nav-link {
            color: white;
            padding: 10px 15px;
            transition: all 0.3s ease;
            border-radius: 5px;
            margin-bottom: 5px;
        }
        .sidebar .nav-link:hover {
            background-color: #495057;
            transform: translateX(3px);
        }
        .sidebar .nav-link.active {
            background-color: var(--primary-color);
            color: white;
        }
        .content {
            padding: 20px;
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
            margin-top: 20px;
        }
        .card {
            border: none;
            border-radius: 10px;
            box-shadow: 0 4px 12px var(--card-shadow);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 6px 20px var(--card-hover-shadow);
        }
        .card-header {
            background-color: var(--primary-color);
            color: white;
            border-radius: 10px 10px 0 0;
            font-weight: bold;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .btn {
            border-radius: 5px;
            transition: all 0.3s ease;
        }
        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }
        .btn-primary:hover {
            background-color: #0056b3;
            border-color: #0056b3;
            transform: translateY(-2px);
        }
        .btn-secondary {
            background-color: var(--secondary-color);
            border-color: var(--secondary-color);
        }
        .btn-secondary:hover {
            background-color: #5a6268;
            border-color: #5a6268;
            transform: translateY(-2px);
        }
        .form-control,
        .form-select {
            border-radius: 5px;
            border: 1px solid #ced4da;
        }
        .form-control:focus,
        .form-select:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }
        .alert {
            border-radius: 8px;
            font-size: 0.95rem;
        }
        @media (max-width: 768px) {
            .sidebar {
                height: auto;
                padding-bottom: 10px;
            }
            .content {
                padding-top: 10px;
            }
            .navbar-nav .nav-item {
                text-align: center;
            }
            .navbar-brand {
                text-align: center;
                width: 100%;
            }
        }

        /* Custom styles for employee_details.html */
        .employee-photo {
            width: 200px;
            height: 200px;
            border-radius: 10px;
            object-fit: cover;
            border: 5px solid #fff;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .employee-photo-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        .employee-name {
            font-size: 1.5rem;
            font-weight: 700;
            color: #343a40;
            margin-top: 10px;
            text-align: center;
        }
        .employee-job {
            font-size: 1.1rem;
            color: #6c757d;
            text-align: center;
            margin-bottom: 10px;
        }
        .employee-id {
            background-color: #e9ecef;
            padding: 5px 15px;
            border-radius: 20px;
            font-weight: 600;
            display: inline-block;
            margin-top: 5px;
            font-size: 0.9rem;
        }
        .detail-row {
            display: flex;
            margin-bottom: 15px;
            border-bottom: 1px dashed #e9ecef;
            padding-bottom: 15px;
        }
        .detail-row:last-child {
            border-bottom: none;
            margin-bottom: 0;
            padding-bottom: 0;
        }
        .detail-label {
            font-weight: 600;
            color: #495057;
            width: 40%;
            padding-left: 15px;
        }
        .detail-value {
            color: #212529;
            width: 60%;
        }
        .page-title {
            color: #343a40;
            text-align: center;
            margin-bottom: 30px;
            font-weight: 700;
            position: relative;
            padding-bottom: 15px;
        }
        .page-title:after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 4px;
            background-color: #007bff;
            border-radius: 2px;
        }
        .document-link {
            display: inline-flex;
            align-items: center;
            padding: 8px 15px;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            color: #495057;
            text-decoration: none;
            margin-right: 10px;
            margin-bottom: 10px;
            transition: all 0.2s ease;
        }
        .document-link:hover {
            background-color: #e9ecef;
            color: #212529;
        }
        .document-link i {
            margin-left: 8px;
            color: #6c757d;
        }
        .documents-container {
            display: flex;
            flex-wrap: wrap;
            margin-top: 10px;
        }
        .no-documents {
            color: #6c757d;
            font-style: italic;
        }
        @media (max-width: 768px) {
            .detail-row {
                flex-direction: column;
            }
            .detail-label, .detail-value {
                width: 100%;
            }
            .detail-label {
                margin-bottom: 5px;
            }
        }
    </style>
    {% block head %}{% endblock %}
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">نظام شؤون الموظفين</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto mb-2 mb-lg-0">
                    <li class="nav-item">
                        <a class="nav-link" href="/dashboard"><i class="fas fa-home"></i> الرئيسية</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/view_employees"><i class="fas fa-users"></i> الموظفون</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('main.manage_departments') }}"><i class="fas fa-building"></i> الأقسام</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/manage_attendance"><i class="fas fa-clock"></i> الحضور</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('main.manage_departures') }}"><i class="fas fa-sign-out-alt"></i> الانصراف</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('main.manage_documents') }}"><i class="fas fa-file-alt"></i> الوثائق</a>
                    </li>
                </ul>
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('main.logout') }}"><i class="fas fa-sign-out-alt"></i> تسجيل الخروج</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <nav id="sidebar" class="col-md-3 col-lg-2 d-md-block bg-dark sidebar collapse">
                <div class="position-sticky pt-3">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link active" aria-current="page" href="{{ url_for('main.dashboard') }}">
                                <i class="fas fa-home"></i>
                                لوحة التحكم
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('main.add_employee') }}">
                                <i class="fas fa-user-plus"></i>
                                إضافة موظف
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('main.view_employees') }}">
                                <i class="fas fa-users"></i>
                                عرض الموظفين
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('main.manage_departments') }}">
                                <i class="fas fa-building"></i>
                                إدارة الأقسام
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('main.manage_attendance') }}">
                                <i class="fas fa-clipboard-check"></i>
                                إدارة الحضور
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('main.manage_leaves') }}">
                                <i class="fas fa-calendar-alt"></i>
                                إدارة الإجازات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('main.manage_departures') }}">
                                <i class="fas fa-plane-departure"></i>
                                إدارة المغادرات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('main.manage_documents') }}">
                                <i class="fas fa-file-alt"></i>
                                إدارة الوثائق
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('main.import_employees') }}">
                                <i class="fas fa-file-excel"></i>
                                استيراد بيانات الموظفين
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('main.export_employees') }}">
                                <i class="fas fa-download"></i>
                                تصدير بيانات الموظفين
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('main.backup_database') }}">
                                <i class="fas fa-database"></i>
                                نسخ احتياطي لقاعدة البيانات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('main.restore_database') }}">
                                <i class="fas fa-undo"></i>
                                استعادة قاعدة البيانات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('main.logout') }}">
                                <i class="fas fa-sign-out-alt"></i>
                                تسجيل الخروج
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- Main content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">{% block page_title %}{% endblock %}</h1>
                </div>

                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        <div class="flash-messages mt-3">
                            {% for category, message in messages %}
                                <div class="alert alert-{{ category }} alert-dismissible fade show fade-in" role="alert" data-auto-hide="5000">
                                    <i class="fas fa-{% if category == 'success' %}check-circle{% elif category == 'danger' %}exclamation-triangle{% elif category == 'warning' %}exclamation-circle{% else %}info-circle{% endif %} me-2"></i>
                                    {{ message }}
                                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="إغلاق"></button>
                                </div>
                            {% endfor %}
                        </div>
                    {% endif %}
                {% endwith %}

                {% block content %}{% endblock %}
            </main>
        </div>
    </div>

    <!-- Bootstrap Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Enhanced Custom JavaScript -->
    <script src="{{ url_for('static', filename='js/enhanced-app.js') }}"></script>
    {% block scripts %}{% endblock %}
</body>
</html>