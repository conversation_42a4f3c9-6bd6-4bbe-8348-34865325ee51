"""
نظام التقارير المحسن
"""

from datetime import datetime, timedelta
from sqlalchemy import func, and_, or_
from models import Employee, Department, Attendance, Leave, Document, Departure
from app_init import db
import pandas as pd
from io import BytesIO
import matplotlib.pyplot as plt
import seaborn as sns
from matplotlib.backends.backend_pdf import PdfPages
import base64
from collections import defaultdict

# إعداد matplotlib للغة العربية
plt.rcParams['font.family'] = ['Arial Unicode MS', 'Tahoma', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class ReportGenerator:
    """مولد التقارير"""
    
    def __init__(self):
        self.current_date = datetime.now()
        self.current_month = self.current_date.month
        self.current_year = self.current_date.year
    
    def get_employees_summary(self):
        """تقرير ملخص الموظفين"""
        total_employees = Employee.query.count()
        active_employees = Employee.query.filter_by(is_active=True).count()
        inactive_employees = total_employees - active_employees
        
        # الموظفون الجدد هذا الشهر
        start_of_month = datetime(self.current_year, self.current_month, 1)
        new_employees = Employee.query.filter(
            Employee.hire_date >= start_of_month
        ).count()
        
        # التوزيع حسب الأقسام
        departments_data = db.session.query(
            Department.name,
            func.count(Employee.id).label('employee_count')
        ).outerjoin(Employee).group_by(Department.id, Department.name).all()
        
        # التوزيع حسب الجنس
        gender_data = db.session.query(
            Employee.gender,
            func.count(Employee.id).label('count')
        ).group_by(Employee.gender).all()
        
        return {
            'total_employees': total_employees,
            'active_employees': active_employees,
            'inactive_employees': inactive_employees,
            'new_employees': new_employees,
            'departments_distribution': departments_data,
            'gender_distribution': gender_data
        }
    
    def get_attendance_report(self, start_date=None, end_date=None, department_id=None):
        """تقرير الحضور والانصراف"""
        if not start_date:
            start_date = datetime(self.current_year, self.current_month, 1)
        if not end_date:
            end_date = self.current_date
        
        query = db.session.query(
            Employee.full_name,
            Employee.national_id,
            Department.name.label('department_name'),
            func.count(Attendance.id).label('attendance_days'),
            func.count(case([(Attendance.status == 'حاضر', 1)])).label('present_days'),
            func.count(case([(Attendance.status == 'غائب', 1)])).label('absent_days'),
            func.count(case([(Attendance.status == 'متأخر', 1)])).label('late_days')
        ).join(Department, Employee.department_id == Department.id, isouter=True)\
         .join(Attendance, Employee.id == Attendance.employee_id, isouter=True)\
         .filter(and_(
             Attendance.date >= start_date,
             Attendance.date <= end_date
         ))
        
        if department_id:
            query = query.filter(Employee.department_id == department_id)
        
        attendance_data = query.group_by(
            Employee.id, Employee.full_name, Employee.national_id, Department.name
        ).all()
        
        # إحصائيات عامة
        total_attendance = Attendance.query.filter(
            and_(Attendance.date >= start_date, Attendance.date <= end_date)
        ).count()
        
        present_count = Attendance.query.filter(
            and_(
                Attendance.date >= start_date,
                Attendance.date <= end_date,
                Attendance.status == 'حاضر'
            )
        ).count()
        
        absent_count = Attendance.query.filter(
            and_(
                Attendance.date >= start_date,
                Attendance.date <= end_date,
                Attendance.status == 'غائب'
            )
        ).count()
        
        late_count = Attendance.query.filter(
            and_(
                Attendance.date >= start_date,
                Attendance.date <= end_date,
                Attendance.status == 'متأخر'
            )
        ).count()
        
        return {
            'attendance_data': attendance_data,
            'summary': {
                'total_attendance': total_attendance,
                'present_count': present_count,
                'absent_count': absent_count,
                'late_count': late_count,
                'attendance_rate': (present_count / total_attendance * 100) if total_attendance > 0 else 0
            },
            'period': {
                'start_date': start_date,
                'end_date': end_date
            }
        }
    
    def get_leaves_report(self, start_date=None, end_date=None, status=None):
        """تقرير الإجازات"""
        if not start_date:
            start_date = datetime(self.current_year, self.current_month, 1)
        if not end_date:
            end_date = self.current_date
        
        query = db.session.query(
            Employee.full_name,
            Employee.national_id,
            Department.name.label('department_name'),
            Leave.leave_type,
            Leave.start_date,
            Leave.end_date,
            Leave.days_count,
            Leave.status,
            Leave.reason
        ).join(Employee, Leave.employee_id == Employee.id)\
         .join(Department, Employee.department_id == Department.id, isouter=True)\
         .filter(and_(
             Leave.start_date >= start_date,
             Leave.start_date <= end_date
         ))
        
        if status:
            query = query.filter(Leave.status == status)
        
        leaves_data = query.all()
        
        # إحصائيات الإجازات
        leaves_summary = db.session.query(
            Leave.leave_type,
            Leave.status,
            func.count(Leave.id).label('count'),
            func.sum(Leave.days_count).label('total_days')
        ).filter(and_(
            Leave.start_date >= start_date,
            Leave.start_date <= end_date
        )).group_by(Leave.leave_type, Leave.status).all()
        
        return {
            'leaves_data': leaves_data,
            'leaves_summary': leaves_summary,
            'period': {
                'start_date': start_date,
                'end_date': end_date
            }
        }
    
    def get_departments_report(self):
        """تقرير الأقسام"""
        departments_data = db.session.query(
            Department.name,
            Department.description,
            func.count(Employee.id).label('employee_count'),
            func.avg(Employee.salary).label('avg_salary'),
            func.sum(Employee.salary).label('total_salary')
        ).outerjoin(Employee, and_(
            Employee.department_id == Department.id,
            Employee.is_active == True
        )).group_by(Department.id, Department.name, Department.description).all()
        
        return {
            'departments_data': departments_data,
            'total_departments': Department.query.count()
        }
    
    def generate_chart(self, chart_type, data, title, labels=None):
        """إنشاء الرسوم البيانية"""
        plt.figure(figsize=(10, 6))
        
        if chart_type == 'pie':
            plt.pie(data, labels=labels, autopct='%1.1f%%', startangle=90)
            plt.axis('equal')
        elif chart_type == 'bar':
            plt.bar(labels, data)
            plt.xticks(rotation=45)
        elif chart_type == 'line':
            plt.plot(labels, data, marker='o')
            plt.xticks(rotation=45)
        
        plt.title(title, fontsize=14, fontweight='bold')
        plt.tight_layout()
        
        # تحويل الرسم إلى base64
        buffer = BytesIO()
        plt.savefig(buffer, format='png', dpi=300, bbox_inches='tight')
        buffer.seek(0)
        chart_data = base64.b64encode(buffer.getvalue()).decode()
        plt.close()
        
        return chart_data
    
    def export_to_excel(self, data, filename, sheet_names=None):
        """تصدير البيانات إلى Excel"""
        output = BytesIO()
        
        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            if isinstance(data, dict):
                for sheet_name, sheet_data in data.items():
                    df = pd.DataFrame(sheet_data)
                    df.to_excel(writer, sheet_name=sheet_name, index=False)
            else:
                df = pd.DataFrame(data)
                df.to_excel(writer, sheet_name='البيانات', index=False)
        
        output.seek(0)
        return output
    
    def get_monthly_trends(self, months=12):
        """الحصول على الاتجاهات الشهرية"""
        end_date = self.current_date
        start_date = end_date - timedelta(days=months * 30)
        
        # اتجاه التوظيف
        hiring_trend = db.session.query(
            func.date_format(Employee.hire_date, '%Y-%m').label('month'),
            func.count(Employee.id).label('hires')
        ).filter(
            Employee.hire_date >= start_date
        ).group_by(func.date_format(Employee.hire_date, '%Y-%m')).all()
        
        # اتجاه الحضور
        attendance_trend = db.session.query(
            func.date_format(Attendance.date, '%Y-%m').label('month'),
            func.count(case([(Attendance.status == 'حاضر', 1)])).label('present'),
            func.count(case([(Attendance.status == 'غائب', 1)])).label('absent')
        ).filter(
            Attendance.date >= start_date
        ).group_by(func.date_format(Attendance.date, '%Y-%m')).all()
        
        return {
            'hiring_trend': hiring_trend,
            'attendance_trend': attendance_trend
        }
    
    def get_employee_performance_report(self, employee_id, start_date=None, end_date=None):
        """تقرير أداء موظف محدد"""
        if not start_date:
            start_date = datetime(self.current_year, self.current_month, 1)
        if not end_date:
            end_date = self.current_date
        
        employee = Employee.query.get(employee_id)
        if not employee:
            return None
        
        # بيانات الحضور
        attendance_data = Attendance.query.filter(
            and_(
                Attendance.employee_id == employee_id,
                Attendance.date >= start_date,
                Attendance.date <= end_date
            )
        ).all()
        
        # بيانات الإجازات
        leaves_data = Leave.query.filter(
            and_(
                Leave.employee_id == employee_id,
                Leave.start_date >= start_date,
                Leave.start_date <= end_date
            )
        ).all()
        
        # بيانات المغادرات
        departures_data = Departure.query.filter(
            and_(
                Departure.employee_id == employee_id,
                Departure.departure_date >= start_date,
                Departure.departure_date <= end_date
            )
        ).all()
        
        return {
            'employee': employee,
            'attendance_data': attendance_data,
            'leaves_data': leaves_data,
            'departures_data': departures_data,
            'period': {
                'start_date': start_date,
                'end_date': end_date
            }
        }

# دالة مساعدة لـ case في SQLAlchemy
def case(conditions):
    """دالة مساعدة لإنشاء case statements"""
    from sqlalchemy import case as sql_case
    return sql_case(conditions)

# إنشاء مثيل من مولد التقارير
report_generator = ReportGenerator()
