from app_init import create_app
from models import db, User
from werkzeug.security import generate_password_hash

app = create_app()

def create_default_user():
    """إنشاء مستخدم افتراضي للنظام"""
    # التحقق من وجود مستخدم افتراضي
    admin_user = User.query.filter_by(username='admin').first()
    if not admin_user:
        # إنشاء مستخدم افتراضي
        default_user = User(
            username='admin',
            email='<EMAIL>',
            password_hash=generate_password_hash('admin123'),
            role='admin'
        )
        db.session.add(default_user)
        db.session.commit()
        print("✅ تم إنشاء المستخدم الافتراضي:")
        print("   اسم المستخدم: admin")
        print("   كلمة المرور: admin123")
        print("   البريد الإلكتروني: <EMAIL>")
    else:
        print("✅ المستخدم الافتراضي موجود بالفعل:")
        print("   اسم المستخدم: admin")
        print("   كلمة المرور: admin123")

if __name__ == '__main__':
    with app.app_context():
        db.create_all() # إنشاء الجداول في قاعدة البيانات
        create_default_user() # إنشاء المستخدم الافتراضي
    app.run(debug=True)