# ملف المتغيرات البيئية - نظام شؤون الموظفين
# Environment Variables - Employee Management System

# إعدادات Flask الأساسية
# Flask Basic Settings
FLASK_APP=app.py
FLASK_ENV=development
SECRET_KEY=your-very-secret-key-change-this-in-production

# إعدادات قاعدة البيانات
# Database Settings
DATABASE_URL=sqlite:///employees.db
# للاستخدام مع PostgreSQL:
# DATABASE_URL=postgresql://employee_user:password@localhost:5432/employees
# للاستخدام مع MySQL:
# DATABASE_URL=mysql://employee_user:password@localhost:3306/employees

# إعدادات Redis للتخزين المؤقت
# Redis Cache Settings
REDIS_URL=redis://localhost:6379/0
CACHE_TYPE=redis
CACHE_DEFAULT_TIMEOUT=300

# إعدادات الأمان
# Security Settings
WTF_CSRF_ENABLED=True
WTF_CSRF_TIME_LIMIT=3600
SESSION_COOKIE_SECURE=True
SESSION_COOKIE_HTTPONLY=True
SESSION_COOKIE_SAMESITE=Lax
PERMANENT_SESSION_LIFETIME=1800

# إعدادات رفع الملفات
# File Upload Settings
MAX_CONTENT_LENGTH=16777216  # 16MB
UPLOAD_FOLDER=static/uploads
ALLOWED_EXTENSIONS=pdf,jpg,jpeg,png,gif,doc,docx

# إعدادات البريد الإلكتروني (اختياري)
# Email Settings (Optional)
MAIL_SERVER=smtp.gmail.com
MAIL_PORT=587
MAIL_USE_TLS=True
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password
MAIL_DEFAULT_SENDER=<EMAIL>

# إعدادات التسجيل
# Logging Settings
LOG_LEVEL=INFO
LOG_FILE=logs/app.log
LOG_MAX_BYTES=10485760  # 10MB
LOG_BACKUP_COUNT=5

# إعدادات الأداء
# Performance Settings
SQLALCHEMY_POOL_SIZE=10
SQLALCHEMY_POOL_TIMEOUT=20
SQLALCHEMY_POOL_RECYCLE=3600
SQLALCHEMY_MAX_OVERFLOW=20

# إعدادات PostgreSQL (إذا كنت تستخدم PostgreSQL)
# PostgreSQL Settings (if using PostgreSQL)
POSTGRES_DB=employees
POSTGRES_USER=employee_user
POSTGRES_PASSWORD=change-this-password
POSTGRES_HOST=localhost
POSTGRES_PORT=5432

# إعدادات pgAdmin
# pgAdmin Settings
PGADMIN_DEFAULT_EMAIL=<EMAIL>
PGADMIN_DEFAULT_PASSWORD=admin123

# إعدادات التطوير
# Development Settings
DEBUG=True
TESTING=False
EXPLAIN_TEMPLATE_LOADING=False

# إعدادات الإنتاج
# Production Settings (uncomment for production)
# DEBUG=False
# TESTING=False
# SSL_REDIRECT=True
# PREFERRED_URL_SCHEME=https

# إعدادات التقارير
# Reports Settings
REPORTS_CACHE_TIMEOUT=1800
CHARTS_DPI=100
CHARTS_FORMAT=png

# إعدادات البحث
# Search Settings
SEARCH_RESULTS_PER_PAGE=20
SEARCH_CACHE_TIMEOUT=600

# إعدادات النسخ الاحتياطي
# Backup Settings
BACKUP_ENABLED=True
BACKUP_INTERVAL=86400  # 24 hours
BACKUP_RETENTION_DAYS=30
BACKUP_PATH=backups/

# إعدادات المراقبة
# Monitoring Settings
PERFORMANCE_MONITORING=True
SLOW_QUERY_THRESHOLD=0.1
REQUEST_TIMEOUT_THRESHOLD=1.0

# إعدادات التوطين
# Localization Settings
DEFAULT_LANGUAGE=ar
TIMEZONE=Asia/Riyadh
DATE_FORMAT=%Y-%m-%d
TIME_FORMAT=%H:%M:%S
DATETIME_FORMAT=%Y-%m-%d %H:%M:%S

# إعدادات الشركة
# Company Settings
COMPANY_NAME=شركة المثال
COMPANY_NAME_EN=Example Company
COMPANY_ADDRESS=الرياض، المملكة العربية السعودية
COMPANY_PHONE=+966-11-1234567
COMPANY_EMAIL=<EMAIL>
COMPANY_WEBSITE=https://www.company.com

# إعدادات النظام
# System Settings
SYSTEM_NAME=نظام إدارة شؤون الموظفين
SYSTEM_VERSION=1.0.0
SYSTEM_ADMIN_EMAIL=<EMAIL>
SYSTEM_SUPPORT_EMAIL=<EMAIL>

# إعدادات API (للاستخدام المستقبلي)
# API Settings (for future use)
API_ENABLED=False
API_VERSION=v1
API_RATE_LIMIT=100
API_RATE_LIMIT_PERIOD=3600
