"""
نظام البحث المتقدم
"""

from sqlalchemy import and_, or_, func, text
from models import Employee, Department, Attendance, Leave, Document, Departure
from app_init import db
from datetime import datetime, timedelta
import re

class AdvancedSearch:
    """فئة البحث المتقدم"""
    
    def __init__(self):
        self.query_builders = {
            'employees': self._build_employee_query,
            'attendance': self._build_attendance_query,
            'leaves': self._build_leaves_query,
            'documents': self._build_documents_query,
            'departures': self._build_departures_query
        }
    
    def search(self, search_type, filters):
        """البحث الرئيسي"""
        if search_type not in self.query_builders:
            raise ValueError(f"نوع البحث غير مدعوم: {search_type}")
        
        query_builder = self.query_builders[search_type]
        return query_builder(filters)
    
    def _build_employee_query(self, filters):
        """بناء استعلام البحث في الموظفين"""
        query = db.session.query(Employee).join(Department, Employee.department_id == Department.id, isouter=True)
        
        # البحث النصي
        if filters.get('search_text'):
            search_text = f"%{filters['search_text']}%"
            query = query.filter(
                or_(
                    Employee.full_name.like(search_text),
                    Employee.national_id.like(search_text),
                    Employee.phone.like(search_text),
                    Employee.email.like(search_text),
                    Employee.job_title.like(search_text)
                )
            )
        
        # تصفية حسب القسم
        if filters.get('department_id'):
            query = query.filter(Employee.department_id == filters['department_id'])
        
        # تصفية حسب المسمى الوظيفي
        if filters.get('job_title'):
            query = query.filter(Employee.job_title.like(f"%{filters['job_title']}%"))
        
        # تصفية حسب الجنس
        if filters.get('gender'):
            query = query.filter(Employee.gender == filters['gender'])
        
        # تصفية حسب الجنسية
        if filters.get('nationality'):
            query = query.filter(Employee.nationality.like(f"%{filters['nationality']}%"))
        
        # تصفية حسب الحالة الاجتماعية
        if filters.get('marital_status'):
            query = query.filter(Employee.marital_status == filters['marital_status'])
        
        # تصفية حسب الحالة (نشط/غير نشط)
        if filters.get('is_active') is not None:
            query = query.filter(Employee.is_active == filters['is_active'])
        
        # تصفية حسب تاريخ التوظيف
        if filters.get('hire_date_from'):
            query = query.filter(Employee.hire_date >= filters['hire_date_from'])
        
        if filters.get('hire_date_to'):
            query = query.filter(Employee.hire_date <= filters['hire_date_to'])
        
        # تصفية حسب تاريخ الميلاد
        if filters.get('birth_date_from'):
            query = query.filter(Employee.birth_date >= filters['birth_date_from'])
        
        if filters.get('birth_date_to'):
            query = query.filter(Employee.birth_date <= filters['birth_date_to'])
        
        # تصفية حسب الراتب
        if filters.get('salary_min'):
            query = query.filter(Employee.salary >= filters['salary_min'])
        
        if filters.get('salary_max'):
            query = query.filter(Employee.salary <= filters['salary_max'])
        
        # تصفية حسب العمر
        if filters.get('age_min') or filters.get('age_max'):
            current_date = datetime.now().date()
            
            if filters.get('age_min'):
                max_birth_date = current_date - timedelta(days=filters['age_min'] * 365)
                query = query.filter(Employee.birth_date <= max_birth_date)
            
            if filters.get('age_max'):
                min_birth_date = current_date - timedelta(days=(filters['age_max'] + 1) * 365)
                query = query.filter(Employee.birth_date >= min_birth_date)
        
        # ترتيب النتائج
        sort_by = filters.get('sort_by', 'full_name')
        sort_order = filters.get('sort_order', 'asc')
        
        if hasattr(Employee, sort_by):
            if sort_order == 'desc':
                query = query.order_by(getattr(Employee, sort_by).desc())
            else:
                query = query.order_by(getattr(Employee, sort_by))
        
        return query
    
    def _build_attendance_query(self, filters):
        """بناء استعلام البحث في الحضور"""
        query = db.session.query(Attendance).join(Employee)
        
        # تصفية حسب الموظف
        if filters.get('employee_id'):
            query = query.filter(Attendance.employee_id == filters['employee_id'])
        
        # تصفية حسب اسم الموظف
        if filters.get('employee_name'):
            query = query.filter(Employee.full_name.like(f"%{filters['employee_name']}%"))
        
        # تصفية حسب التاريخ
        if filters.get('date_from'):
            query = query.filter(Attendance.date >= filters['date_from'])
        
        if filters.get('date_to'):
            query = query.filter(Attendance.date <= filters['date_to'])
        
        # تصفية حسب الحالة
        if filters.get('status'):
            query = query.filter(Attendance.status == filters['status'])
        
        # تصفية حسب القسم
        if filters.get('department_id'):
            query = query.filter(Employee.department_id == filters['department_id'])
        
        # تصفية حسب وقت الحضور
        if filters.get('check_in_from'):
            query = query.filter(Attendance.check_in_time >= filters['check_in_from'])
        
        if filters.get('check_in_to'):
            query = query.filter(Attendance.check_in_time <= filters['check_in_to'])
        
        return query.order_by(Attendance.date.desc())
    
    def _build_leaves_query(self, filters):
        """بناء استعلام البحث في الإجازات"""
        query = db.session.query(Leave).join(Employee)
        
        # تصفية حسب الموظف
        if filters.get('employee_id'):
            query = query.filter(Leave.employee_id == filters['employee_id'])
        
        # تصفية حسب اسم الموظف
        if filters.get('employee_name'):
            query = query.filter(Employee.full_name.like(f"%{filters['employee_name']}%"))
        
        # تصفية حسب نوع الإجازة
        if filters.get('leave_type'):
            query = query.filter(Leave.leave_type == filters['leave_type'])
        
        # تصفية حسب حالة الإجازة
        if filters.get('status'):
            query = query.filter(Leave.status == filters['status'])
        
        # تصفية حسب تاريخ البداية
        if filters.get('start_date_from'):
            query = query.filter(Leave.start_date >= filters['start_date_from'])
        
        if filters.get('start_date_to'):
            query = query.filter(Leave.start_date <= filters['start_date_to'])
        
        # تصفية حسب عدد الأيام
        if filters.get('days_min'):
            query = query.filter(Leave.days_count >= filters['days_min'])
        
        if filters.get('days_max'):
            query = query.filter(Leave.days_count <= filters['days_max'])
        
        # تصفية حسب القسم
        if filters.get('department_id'):
            query = query.filter(Employee.department_id == filters['department_id'])
        
        return query.order_by(Leave.start_date.desc())
    
    def _build_documents_query(self, filters):
        """بناء استعلام البحث في الوثائق"""
        query = db.session.query(Document).join(Employee)
        
        # تصفية حسب الموظف
        if filters.get('employee_id'):
            query = query.filter(Document.employee_id == filters['employee_id'])
        
        # تصفية حسب اسم الموظف
        if filters.get('employee_name'):
            query = query.filter(Employee.full_name.like(f"%{filters['employee_name']}%"))
        
        # تصفية حسب نوع الوثيقة
        if filters.get('document_type'):
            query = query.filter(Document.document_type.like(f"%{filters['document_type']}%"))
        
        # تصفية حسب تاريخ الرفع
        if filters.get('upload_date_from'):
            query = query.filter(Document.created_at >= filters['upload_date_from'])
        
        if filters.get('upload_date_to'):
            query = query.filter(Document.created_at <= filters['upload_date_to'])
        
        # تصفية حسب حجم الملف
        if filters.get('file_size_min'):
            query = query.filter(Document.file_size >= filters['file_size_min'])
        
        if filters.get('file_size_max'):
            query = query.filter(Document.file_size <= filters['file_size_max'])
        
        return query.order_by(Document.created_at.desc())
    
    def _build_departures_query(self, filters):
        """بناء استعلام البحث في المغادرات"""
        query = db.session.query(Departure).join(Employee)
        
        # تصفية حسب الموظف
        if filters.get('employee_id'):
            query = query.filter(Departure.employee_id == filters['employee_id'])
        
        # تصفية حسب اسم الموظف
        if filters.get('employee_name'):
            query = query.filter(Employee.full_name.like(f"%{filters['employee_name']}%"))
        
        # تصفية حسب تاريخ المغادرة
        if filters.get('departure_date_from'):
            query = query.filter(Departure.departure_date >= filters['departure_date_from'])
        
        if filters.get('departure_date_to'):
            query = query.filter(Departure.departure_date <= filters['departure_date_to'])
        
        # تصفية حسب سبب المغادرة
        if filters.get('reason'):
            query = query.filter(Departure.reason.like(f"%{filters['reason']}%"))
        
        # تصفية حسب حالة المغادرة
        if filters.get('status'):
            query = query.filter(Departure.status == filters['status'])
        
        return query.order_by(Departure.departure_date.desc())
    
    def get_search_suggestions(self, search_type, field, query_text):
        """الحصول على اقتراحات البحث"""
        suggestions = []
        
        if search_type == 'employees':
            if field == 'full_name':
                results = db.session.query(Employee.full_name).filter(
                    Employee.full_name.like(f"%{query_text}%")
                ).limit(10).all()
                suggestions = [result[0] for result in results]
            
            elif field == 'job_title':
                results = db.session.query(Employee.job_title).filter(
                    Employee.job_title.like(f"%{query_text}%")
                ).distinct().limit(10).all()
                suggestions = [result[0] for result in results if result[0]]
            
            elif field == 'department':
                results = db.session.query(Department.name).filter(
                    Department.name.like(f"%{query_text}%")
                ).limit(10).all()
                suggestions = [result[0] for result in results]
        
        return suggestions
    
    def get_filter_options(self, search_type):
        """الحصول على خيارات التصفية المتاحة"""
        options = {}
        
        if search_type == 'employees':
            # الأقسام
            departments = db.session.query(Department.id, Department.name).all()
            options['departments'] = [{'id': d[0], 'name': d[1]} for d in departments]
            
            # المسميات الوظيفية
            job_titles = db.session.query(Employee.job_title).filter(
                Employee.job_title.isnot(None)
            ).distinct().all()
            options['job_titles'] = [jt[0] for jt in job_titles if jt[0]]
            
            # الجنسيات
            nationalities = db.session.query(Employee.nationality).filter(
                Employee.nationality.isnot(None)
            ).distinct().all()
            options['nationalities'] = [n[0] for n in nationalities if n[0]]
        
        elif search_type == 'leaves':
            # أنواع الإجازات
            leave_types = db.session.query(Leave.leave_type).filter(
                Leave.leave_type.isnot(None)
            ).distinct().all()
            options['leave_types'] = [lt[0] for lt in leave_types if lt[0]]
        
        elif search_type == 'documents':
            # أنواع الوثائق
            document_types = db.session.query(Document.document_type).filter(
                Document.document_type.isnot(None)
            ).distinct().all()
            options['document_types'] = [dt[0] for dt in document_types if dt[0]]
        
        return options
    
    def build_search_query_string(self, filters):
        """بناء نص استعلام البحث للعرض"""
        query_parts = []
        
        for key, value in filters.items():
            if value and key != 'sort_by' and key != 'sort_order':
                if isinstance(value, str):
                    query_parts.append(f"{key}: {value}")
                elif isinstance(value, (int, float)):
                    query_parts.append(f"{key}: {value}")
                elif isinstance(value, datetime):
                    query_parts.append(f"{key}: {value.strftime('%Y-%m-%d')}")
        
        return " | ".join(query_parts) if query_parts else "بحث عام"

# إنشاء مثيل من البحث المتقدم
advanced_search = AdvancedSearch()
