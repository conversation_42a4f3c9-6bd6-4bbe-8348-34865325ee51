# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
#  Usually these files are written by a python script from a template
#  before PyInstaller builds the exe, so as to inject date/other infos into it.
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/
test_report.html

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache
employees.db
employees.db-journal

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
#   According to pypa/pipenv#598, it is recommended to include Pipfile.lock in version control.
#   However, in case of collaboration, if having platform-specific dependencies or dependencies
#   having no cross-platform support, pipenv may install dependencies that don't work, or not
#   install all needed dependencies.
#Pipfile.lock

# PEP 582; used by e.g. github.com/David-OConnor/pyflow
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# Employee Management System specific files
# ملفات خاصة بنظام إدارة الموظفين

# Database files
*.db
*.db-journal
*.sqlite
*.sqlite3

# Upload directories
static/uploads/photos/*
static/uploads/documents/*
!static/uploads/photos/.gitkeep
!static/uploads/documents/.gitkeep

# Log files
logs/
*.log
*.log.*

# Backup files
backups/
*.bak
*.backup

# Cache files
.cache/
cache/
*.cache

# Temporary files
tmp/
temp/
*.tmp
*.temp

# Configuration files with sensitive data
config.py
local_config.py
production_config.py

# SSL certificates
ssl/
*.pem
*.key
*.crt
*.cert

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Docker files
.dockerignore

# Coverage reports
htmlcov/
.coverage
coverage.xml

# Performance monitoring
performance.log
slow_queries.log

# Redis dump
dump.rdb

# Node modules (if using any frontend tools)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Compiled CSS/JS (if using preprocessors)
static/css/compiled/
static/js/compiled/

# Migration files (keep structure but ignore data)
migrations/versions/*.py
!migrations/versions/__init__.py

# Test artifacts
.pytest_cache/
test-results/
test-reports/

# Documentation build
docs/_build/
docs/build/

# Local development files
local_settings.py
dev_config.py
test_config.py

# Jupyter notebooks
*.ipynb

# Virtual environment
venv*/
env*/

# Package files
*.tar.gz
*.zip
*.rar

# Editor backups
*~
*.bak
*.orig

# System files
*.pid
*.seed
*.pid.lock

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# nyc test coverage
.nyc_output

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.parcel-cache

# next.js build output
.next

# nuxt.js build output
.nuxt

# vuepress build output
.vuepress/dist

# Serverless directories
.serverless

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/
