{% extends "base.html" %}

{% block title %}عرض الموظفين - نظام شؤون الموظفين{% endblock %}

{% block page_title %}إدارة الموظفين{% endblock %}

{% block head %}
<style>
    .employees-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 25px;
        border-radius: 15px;
        margin-bottom: 30px;
        box-shadow: 0 8px 30px rgba(0,0,0,0.1);
    }
    
    .search-filters {
        background: white;
        padding: 25px;
        border-radius: 15px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        margin-bottom: 25px;
    }
    
    .employee-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
        overflow: hidden;
        margin-bottom: 20px;
    }
    
    .employee-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    }
    
    .employee-avatar {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        object-fit: cover;
        border: 4px solid white;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    
    .employee-info {
        flex: 1;
        padding: 20px;
    }
    
    .employee-name {
        font-size: 1.25rem;
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 5px;
    }
    
    .employee-title {
        color: #667eea;
        font-weight: 500;
        margin-bottom: 10px;
    }
    
    .employee-details {
        display: flex;
        gap: 20px;
        flex-wrap: wrap;
        margin-bottom: 15px;
    }
    
    .detail-item {
        display: flex;
        align-items: center;
        color: #6c757d;
        font-size: 0.9rem;
    }
    
    .detail-item i {
        margin-left: 8px;
        color: #667eea;
    }
    
    .employee-actions {
        display: flex;
        gap: 10px;
        padding: 20px;
        background: #f8f9fa;
        border-top: 1px solid #e9ecef;
    }
    
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
    }
    
    .stat-card {
        background: white;
        padding: 25px;
        border-radius: 15px;
        text-align: center;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
        border-left: 5px solid;
    }
    
    .stat-card:hover {
        transform: translateY(-3px);
    }
    
    .stat-card.total { border-left-color: #667eea; }
    .stat-card.active { border-left-color: #28a745; }
    .stat-card.inactive { border-left-color: #dc3545; }
    .stat-card.new { border-left-color: #ffc107; }
    
    .stat-number {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 10px;
    }
    
    .stat-label {
        color: #6c757d;
        font-weight: 500;
    }
    
    .view-toggle {
        display: flex;
        gap: 10px;
        margin-bottom: 20px;
    }
    
    .view-btn {
        padding: 10px 15px;
        border: 2px solid #e9ecef;
        background: white;
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.3s ease;
    }
    
    .view-btn.active {
        border-color: #667eea;
        background: #667eea;
        color: white;
    }
    
    .table-view {
        background: white;
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }
    
    .table thead th {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        font-weight: 600;
        border: none;
        padding: 20px 15px;
    }
    
    .table tbody tr {
        transition: all 0.3s ease;
    }
    
    .table tbody tr:hover {
        background-color: #f8f9ff;
    }
    
    .table tbody td {
        padding: 15px;
        vertical-align: middle;
        border-top: 1px solid #e9ecef;
    }
    
    .employee-photo-small {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        object-fit: cover;
    }
    
    .status-badge {
        padding: 5px 12px;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 500;
    }
    
    .status-active {
        background: #d4edda;
        color: #155724;
    }
    
    .status-inactive {
        background: #f8d7da;
        color: #721c24;
    }
    
    @media (max-width: 768px) {
        .employee-details {
            flex-direction: column;
            gap: 10px;
        }
        
        .employee-actions {
            flex-direction: column;
        }
        
        .stats-grid {
            grid-template-columns: repeat(2, 1fr);
        }
        
        .search-filters .row {
            margin-bottom: 15px;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="employees-header fade-in">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h2><i class="fas fa-users me-3"></i>إدارة الموظفين</h2>
                <p class="mb-0">عرض وإدارة جميع بيانات الموظفين في النظام</p>
            </div>
            <a href="{{ url_for('main.add_employee') }}" class="btn btn-light btn-lg">
                <i class="fas fa-user-plus me-2"></i>إضافة موظف جديد
            </a>
        </div>
    </div>
    
    <!-- Statistics -->
    <div class="stats-grid fade-in">
        <div class="stat-card total">
            <div class="stat-number" style="color: #667eea;">{{ employees|length }}</div>
            <div class="stat-label">إجمالي الموظفين</div>
        </div>
        <div class="stat-card active">
            <div class="stat-number" style="color: #28a745;">{{ employees|selectattr('is_active', 'equalto', true)|list|length }}</div>
            <div class="stat-label">الموظفون النشطون</div>
        </div>
        <div class="stat-card inactive">
            <div class="stat-number" style="color: #dc3545;">{{ employees|selectattr('is_active', 'equalto', false)|list|length }}</div>
            <div class="stat-label">الموظفون غير النشطين</div>
        </div>
        <div class="stat-card new">
            <div class="stat-number" style="color: #ffc107;">5</div>
            <div class="stat-label">موظفون جدد هذا الشهر</div>
        </div>
    </div>
    
    <!-- Search and Filters -->
    <div class="search-filters fade-in">
        <h5 class="mb-3"><i class="fas fa-search me-2"></i>البحث والتصفية</h5>
        <div class="row">
            <div class="col-md-4">
                <label class="form-label">البحث بالاسم أو رقم الهوية</label>
                <input type="text" class="form-control" id="searchInput" placeholder="ابحث عن موظف..." data-live-search="tr">
            </div>
            <div class="col-md-3">
                <label class="form-label">القسم</label>
                <select class="form-select" id="departmentFilter">
                    <option value="">جميع الأقسام</option>
                    {% for department in departments %}
                        <option value="{{ department.name }}">{{ department.name }}</option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">الحالة</label>
                <select class="form-select" id="statusFilter">
                    <option value="">جميع الحالات</option>
                    <option value="active">نشط</option>
                    <option value="inactive">غير نشط</option>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <button class="btn btn-primary w-100" onclick="applyFilters()">
                    <i class="fas fa-filter me-2"></i>تطبيق
                </button>
            </div>
        </div>
    </div>
    
    <!-- View Toggle -->
    <div class="view-toggle fade-in">
        <button class="view-btn active" onclick="switchView('cards')">
            <i class="fas fa-th-large me-2"></i>عرض البطاقات
        </button>
        <button class="view-btn" onclick="switchView('table')">
            <i class="fas fa-table me-2"></i>عرض الجدول
        </button>
    </div>
    
    <!-- Cards View -->
    <div id="cardsView" class="fade-in">
        <div class="row">
            {% for employee in employees %}
            <div class="col-lg-6 col-xl-4 employee-item" data-department="{{ employee.department.name if employee.department else '' }}" data-status="{{ 'active' if employee.is_active else 'inactive' }}">
                <div class="employee-card">
                    <div class="d-flex align-items-center p-3">
                        {% if employee.photo %}
                            <img src="{{ url_for('static', filename='uploads/photos/' + employee.photo) }}" alt="صورة {{ employee.full_name }}" class="employee-avatar">
                        {% else %}
                            <div class="employee-avatar d-flex align-items-center justify-content-center" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; font-size: 1.5rem; font-weight: bold;">
                                {{ employee.full_name[0] if employee.full_name else 'م' }}
                            </div>
                        {% endif %}
                        <div class="employee-info">
                            <div class="employee-name">{{ employee.full_name }}</div>
                            <div class="employee-title">{{ employee.job_title }}</div>
                            <div class="employee-details">
                                <div class="detail-item">
                                    <i class="fas fa-id-card"></i>
                                    <span>{{ employee.national_id }}</span>
                                </div>
                                <div class="detail-item">
                                    <i class="fas fa-building"></i>
                                    <span>{{ employee.department.name if employee.department else 'غير محدد' }}</span>
                                </div>
                                <div class="detail-item">
                                    <i class="fas fa-phone"></i>
                                    <span>{{ employee.phone }}</span>
                                </div>
                            </div>
                            <span class="status-badge {{ 'status-active' if employee.is_active else 'status-inactive' }}">
                                {{ 'نشط' if employee.is_active else 'غير نشط' }}
                            </span>
                        </div>
                    </div>
                    <div class="employee-actions">
                        <a href="{{ url_for('main.employee_details', employee_id=employee.id) }}" class="btn btn-primary btn-sm">
                            <i class="fas fa-eye me-1"></i>عرض
                        </a>
                        <a href="{{ url_for('main.edit_employee', employee_id=employee.id) }}" class="btn btn-warning btn-sm">
                            <i class="fas fa-edit me-1"></i>تعديل
                        </a>
                        <a href="{{ url_for('main.delete_employee', employee_id=employee.id) }}" class="btn btn-danger btn-sm" data-confirm="delete">
                            <i class="fas fa-trash me-1"></i>حذف
                        </a>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
    
    <!-- Table View -->
    <div id="tableView" class="table-view" style="display: none;">
        <table class="table table-hover mb-0">
            <thead>
                <tr>
                    <th>الصورة</th>
                    <th>الاسم الكامل</th>
                    <th>رقم الهوية</th>
                    <th>المسمى الوظيفي</th>
                    <th>القسم</th>
                    <th>رقم الهاتف</th>
                    <th>الحالة</th>
                    <th>الإجراءات</th>
                </tr>
            </thead>
            <tbody>
                {% for employee in employees %}
                <tr class="employee-item" data-department="{{ employee.department.name if employee.department else '' }}" data-status="{{ 'active' if employee.is_active else 'inactive' }}">
                    <td>
                        {% if employee.photo %}
                            <img src="{{ url_for('static', filename='uploads/photos/' + employee.photo) }}" alt="صورة {{ employee.full_name }}" class="employee-photo-small">
                        {% else %}
                            <div class="employee-photo-small d-flex align-items-center justify-content-center" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; font-weight: bold;">
                                {{ employee.full_name[0] if employee.full_name else 'م' }}
                            </div>
                        {% endif %}
                    </td>
                    <td><strong>{{ employee.full_name }}</strong></td>
                    <td>{{ employee.national_id }}</td>
                    <td>{{ employee.job_title }}</td>
                    <td>{{ employee.department.name if employee.department else 'غير محدد' }}</td>
                    <td>{{ employee.phone }}</td>
                    <td>
                        <span class="status-badge {{ 'status-active' if employee.is_active else 'status-inactive' }}">
                            {{ 'نشط' if employee.is_active else 'غير نشط' }}
                        </span>
                    </td>
                    <td>
                        <div class="btn-group" role="group">
                            <a href="{{ url_for('main.employee_details', employee_id=employee.id) }}" class="btn btn-primary btn-sm">
                                <i class="fas fa-eye"></i>
                            </a>
                            <a href="{{ url_for('main.edit_employee', employee_id=employee.id) }}" class="btn btn-warning btn-sm">
                                <i class="fas fa-edit"></i>
                            </a>
                            <a href="{{ url_for('main.delete_employee', employee_id=employee.id) }}" class="btn btn-danger btn-sm" data-confirm="delete">
                                <i class="fas fa-trash"></i>
                            </a>
                        </div>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // تبديل العرض بين البطاقات والجدول
    function switchView(viewType) {
        const cardsView = document.getElementById('cardsView');
        const tableView = document.getElementById('tableView');
        const buttons = document.querySelectorAll('.view-btn');
        
        buttons.forEach(btn => btn.classList.remove('active'));
        
        if (viewType === 'cards') {
            cardsView.style.display = 'block';
            tableView.style.display = 'none';
            buttons[0].classList.add('active');
        } else {
            cardsView.style.display = 'none';
            tableView.style.display = 'block';
            buttons[1].classList.add('active');
        }
    }
    
    // تطبيق المرشحات
    function applyFilters() {
        const searchTerm = document.getElementById('searchInput').value.toLowerCase();
        const departmentFilter = document.getElementById('departmentFilter').value;
        const statusFilter = document.getElementById('statusFilter').value;
        const employees = document.querySelectorAll('.employee-item');
        
        employees.forEach(employee => {
            const name = employee.querySelector('.employee-name, strong')?.textContent.toLowerCase() || '';
            const nationalId = employee.querySelector('[data-national-id], td:nth-child(3)')?.textContent || '';
            const department = employee.getAttribute('data-department') || '';
            const status = employee.getAttribute('data-status') || '';
            
            let show = true;
            
            // فلتر البحث
            if (searchTerm && !name.includes(searchTerm) && !nationalId.includes(searchTerm)) {
                show = false;
            }
            
            // فلتر القسم
            if (departmentFilter && department !== departmentFilter) {
                show = false;
            }
            
            // فلتر الحالة
            if (statusFilter && status !== statusFilter) {
                show = false;
            }
            
            employee.style.display = show ? '' : 'none';
        });
    }
    
    // البحث المباشر
    document.getElementById('searchInput').addEventListener('input', applyFilters);
    document.getElementById('departmentFilter').addEventListener('change', applyFilters);
    document.getElementById('statusFilter').addEventListener('change', applyFilters);
</script>
{% endblock %}
