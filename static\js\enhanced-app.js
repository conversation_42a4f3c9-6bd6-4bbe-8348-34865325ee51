// تحسينات JavaScript للتطبيق العربي

// تهيئة التطبيق عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

function initializeApp() {
    // تهيئة التحقق من صحة النماذج
    initializeFormValidation();
    
    // تهيئة التأكيدات
    initializeConfirmations();
    
    // تهيئة التحميل التفاعلي
    initializeLoadingStates();
    
    // تهيئة البحث المباشر
    initializeLiveSearch();
    
    // تهيئة الرسوم المتحركة
    initializeAnimations();
    
    // تهيئة إخفاء الرسائل التلقائي
    initializeAutoHideAlerts();
}

// التحقق من صحة النماذج
function initializeFormValidation() {
    const forms = document.querySelectorAll('form[data-validate="true"]');
    
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            if (!validateForm(this)) {
                e.preventDefault();
                e.stopPropagation();
            }
            this.classList.add('was-validated');
        });
        
        // التحقق المباشر أثناء الكتابة
        const inputs = form.querySelectorAll('input, select, textarea');
        inputs.forEach(input => {
            input.addEventListener('blur', function() {
                validateField(this);
            });
            
            input.addEventListener('input', function() {
                if (this.classList.contains('is-invalid')) {
                    validateField(this);
                }
            });
        });
    });
}

// التحقق من صحة حقل واحد
function validateField(field) {
    const value = field.value.trim();
    const type = field.type;
    const required = field.hasAttribute('required');
    let isValid = true;
    let errorMessage = '';
    
    // التحقق من الحقول المطلوبة
    if (required && !value) {
        isValid = false;
        errorMessage = 'هذا الحقل مطلوب';
    }
    
    // التحقق من البريد الإلكتروني
    else if (type === 'email' && value) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(value)) {
            isValid = false;
            errorMessage = 'يرجى إدخال بريد إلكتروني صحيح';
        }
    }
    
    // التحقق من رقم الهوية السعودي
    else if (field.name === 'national_id' && value) {
        if (!validateSaudiNationalId(value)) {
            isValid = false;
            errorMessage = 'رقم الهوية غير صحيح';
        }
    }
    
    // التحقق من رقم الهاتف
    else if (field.name === 'phone' && value) {
        const phoneRegex = /^(05|5)[0-9]{8}$/;
        if (!phoneRegex.test(value.replace(/\s+/g, ''))) {
            isValid = false;
            errorMessage = 'رقم الهاتف غير صحيح (يجب أن يبدأ بـ 05)';
        }
    }
    
    // التحقق من كلمة المرور
    else if (type === 'password' && value) {
        if (value.length < 8) {
            isValid = false;
            errorMessage = 'كلمة المرور يجب أن تكون 8 أحرف على الأقل';
        }
    }
    
    // التحقق من تطابق كلمة المرور
    else if (field.name === 'confirm_password' && value) {
        const passwordField = document.querySelector('input[name="password"]');
        if (passwordField && value !== passwordField.value) {
            isValid = false;
            errorMessage = 'كلمة المرور غير متطابقة';
        }
    }
    
    // عرض النتيجة
    updateFieldValidation(field, isValid, errorMessage);
    return isValid;
}

// تحديث حالة التحقق للحقل
function updateFieldValidation(field, isValid, errorMessage) {
    field.classList.remove('is-valid', 'is-invalid');
    
    // إزالة رسالة الخطأ السابقة
    const existingError = field.parentNode.querySelector('.invalid-feedback');
    if (existingError) {
        existingError.remove();
    }
    
    if (isValid) {
        field.classList.add('is-valid');
    } else {
        field.classList.add('is-invalid');
        
        // إضافة رسالة الخطأ
        const errorDiv = document.createElement('div');
        errorDiv.className = 'invalid-feedback';
        errorDiv.textContent = errorMessage;
        field.parentNode.appendChild(errorDiv);
    }
}

// التحقق من صحة النموذج كاملاً
function validateForm(form) {
    const fields = form.querySelectorAll('input, select, textarea');
    let isFormValid = true;
    
    fields.forEach(field => {
        if (!validateField(field)) {
            isFormValid = false;
        }
    });
    
    return isFormValid;
}

// التحقق من رقم الهوية السعودي
function validateSaudiNationalId(id) {
    if (!/^\d{10}$/.test(id)) return false;
    
    let sum = 0;
    for (let i = 0; i < 9; i++) {
        sum += parseInt(id[i]) * (10 - i);
    }
    
    const remainder = sum % 11;
    const checkDigit = remainder < 2 ? remainder : 11 - remainder;
    
    return checkDigit === parseInt(id[9]);
}

// تهيئة التأكيدات
function initializeConfirmations() {
    const deleteButtons = document.querySelectorAll('[data-confirm="delete"]');
    const actionButtons = document.querySelectorAll('[data-confirm]');
    
    deleteButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            showConfirmDialog(
                'تأكيد الحذف',
                'هل أنت متأكد من أنك تريد حذف هذا العنصر؟ لا يمكن التراجع عن هذا الإجراء.',
                'حذف',
                'إلغاء',
                () => {
                    window.location.href = this.href;
                }
            );
        });
    });
    
    actionButtons.forEach(button => {
        if (!button.hasAttribute('data-confirm') || button.getAttribute('data-confirm') === 'delete') return;
        
        button.addEventListener('click', function(e) {
            e.preventDefault();
            const message = this.getAttribute('data-confirm');
            showConfirmDialog(
                'تأكيد الإجراء',
                message,
                'تأكيد',
                'إلغاء',
                () => {
                    if (this.tagName === 'A') {
                        window.location.href = this.href;
                    } else if (this.type === 'submit') {
                        this.form.submit();
                    }
                }
            );
        });
    });
}

// عرض نافذة التأكيد
function showConfirmDialog(title, message, confirmText, cancelText, onConfirm) {
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.innerHTML = `
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">${title}</h5>
                </div>
                <div class="modal-body">
                    <p>${message}</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">${cancelText}</button>
                    <button type="button" class="btn btn-danger confirm-btn">${confirmText}</button>
                </div>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
    
    const bootstrapModal = new bootstrap.Modal(modal);
    bootstrapModal.show();
    
    modal.querySelector('.confirm-btn').addEventListener('click', function() {
        onConfirm();
        bootstrapModal.hide();
    });
    
    modal.addEventListener('hidden.bs.modal', function() {
        document.body.removeChild(modal);
    });
}

// تهيئة حالات التحميل
function initializeLoadingStates() {
    const forms = document.querySelectorAll('form');
    
    forms.forEach(form => {
        form.addEventListener('submit', function() {
            const submitButton = this.querySelector('button[type="submit"], input[type="submit"]');
            if (submitButton) {
                showLoadingState(submitButton);
            }
        });
    });
}

// عرض حالة التحميل
function showLoadingState(button) {
    const originalText = button.textContent;
    button.disabled = true;
    button.innerHTML = '<span class="loading-spinner"></span> جاري التحميل...';
    
    // إعادة تعيين الحالة بعد 10 ثوان (في حالة عدم إعادة تحميل الصفحة)
    setTimeout(() => {
        button.disabled = false;
        button.textContent = originalText;
    }, 10000);
}

// تهيئة البحث المباشر
function initializeLiveSearch() {
    const searchInputs = document.querySelectorAll('[data-live-search]');
    
    searchInputs.forEach(input => {
        let timeout;
        input.addEventListener('input', function() {
            clearTimeout(timeout);
            timeout = setTimeout(() => {
                performLiveSearch(this);
            }, 300);
        });
    });
}

// تنفيذ البحث المباشر
function performLiveSearch(input) {
    const searchTerm = input.value.toLowerCase();
    const targetSelector = input.getAttribute('data-live-search');
    const targets = document.querySelectorAll(targetSelector);
    
    targets.forEach(target => {
        const text = target.textContent.toLowerCase();
        if (text.includes(searchTerm)) {
            target.style.display = '';
        } else {
            target.style.display = 'none';
        }
    });
}

// تهيئة الرسوم المتحركة
function initializeAnimations() {
    // إضافة الرسوم المتحركة للعناصر عند الظهور
    const animatedElements = document.querySelectorAll('.fade-in, .slide-in');
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    });
    
    animatedElements.forEach(element => {
        observer.observe(element);
    });
}

// تهيئة إخفاء الرسائل التلقائي
function initializeAutoHideAlerts() {
    const alerts = document.querySelectorAll('.alert[data-auto-hide]');
    
    alerts.forEach(alert => {
        const delay = parseInt(alert.getAttribute('data-auto-hide')) || 5000;
        setTimeout(() => {
            alert.style.opacity = '0';
            setTimeout(() => {
                alert.remove();
            }, 300);
        }, delay);
    });
}

// دوال مساعدة إضافية

// تنسيق الأرقام العربية
function formatArabicNumber(number) {
    return number.toLocaleString('ar-SA');
}

// تنسيق التاريخ العربي
function formatArabicDate(date) {
    return new Date(date).toLocaleDateString('ar-SA', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });
}

// عرض رسالة نجاح
function showSuccessMessage(message) {
    showAlert(message, 'success');
}

// عرض رسالة خطأ
function showErrorMessage(message) {
    showAlert(message, 'danger');
}

// عرض رسالة تنبيه
function showAlert(message, type) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    const container = document.querySelector('.container, .container-fluid');
    if (container) {
        container.insertBefore(alertDiv, container.firstChild);
        
        // إخفاء الرسالة تلقائياً بعد 5 ثوان
        setTimeout(() => {
            alertDiv.remove();
        }, 5000);
    }
}
