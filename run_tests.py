"""
تشغيل جميع الاختبارات
"""

import unittest
import sys
import os
from datetime import datetime
import coverage

# إضافة المجلد الحالي إلى المسار
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def run_all_tests():
    """تشغيل جميع الاختبارات"""
    print("=" * 60)
    print("تشغيل اختبارات نظام شؤون الموظفين")
    print("=" * 60)
    print(f"تاريخ التشغيل: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # إعداد تغطية الكود
    cov = coverage.Coverage()
    cov.start()
    
    # البحث عن جميع ملفات الاختبار
    test_dir = os.path.join(os.path.dirname(__file__), 'tests')
    test_loader = unittest.TestLoader()
    
    # تحميل الاختبارات
    test_suite = unittest.TestSuite()
    
    # إضافة اختبارات النماذج
    try:
        from tests.test_models import TestModels
        model_tests = test_loader.loadTestsFromTestCase(TestModels)
        test_suite.addTests(model_tests)
        print("✓ تم تحميل اختبارات النماذج")
    except ImportError as e:
        print(f"✗ فشل في تحميل اختبارات النماذج: {e}")
    
    # إضافة اختبارات الدوال
    try:
        from tests.test_functions import TestValidators, TestUtils, TestFileOperations, TestDatabaseOperations
        function_tests = [
            test_loader.loadTestsFromTestCase(TestValidators),
            test_loader.loadTestsFromTestCase(TestUtils),
            test_loader.loadTestsFromTestCase(TestFileOperations),
            test_loader.loadTestsFromTestCase(TestDatabaseOperations)
        ]
        for tests in function_tests:
            test_suite.addTests(tests)
        print("✓ تم تحميل اختبارات الدوال")
    except ImportError as e:
        print(f"✗ فشل في تحميل اختبارات الدوال: {e}")
    
    print()
    print("بدء تشغيل الاختبارات...")
    print("-" * 60)
    
    # تشغيل الاختبارات
    runner = unittest.TextTestRunner(verbosity=2, stream=sys.stdout)
    result = runner.run(test_suite)
    
    # إيقاف تغطية الكود
    cov.stop()
    cov.save()
    
    print()
    print("=" * 60)
    print("ملخص النتائج")
    print("=" * 60)
    
    # عرض النتائج
    total_tests = result.testsRun
    failures = len(result.failures)
    errors = len(result.errors)
    skipped = len(result.skipped) if hasattr(result, 'skipped') else 0
    successful = total_tests - failures - errors - skipped
    
    print(f"إجمالي الاختبارات: {total_tests}")
    print(f"نجح: {successful}")
    print(f"فشل: {failures}")
    print(f"أخطاء: {errors}")
    print(f"تم تخطيه: {skipped}")
    
    success_rate = (successful / total_tests * 100) if total_tests > 0 else 0
    print(f"معدل النجاح: {success_rate:.1f}%")
    
    # عرض تفاصيل الفشل
    if result.failures:
        print()
        print("تفاصيل الاختبارات الفاشلة:")
        print("-" * 40)
        for test, traceback in result.failures:
            print(f"الاختبار: {test}")
            print(f"السبب: {traceback}")
            print("-" * 40)
    
    # عرض تفاصيل الأخطاء
    if result.errors:
        print()
        print("تفاصيل الأخطاء:")
        print("-" * 40)
        for test, traceback in result.errors:
            print(f"الاختبار: {test}")
            print(f"الخطأ: {traceback}")
            print("-" * 40)
    
    # عرض تقرير تغطية الكود
    print()
    print("تقرير تغطية الكود:")
    print("-" * 40)
    try:
        cov.report(show_missing=True)
    except Exception as e:
        print(f"فشل في إنشاء تقرير التغطية: {e}")
    
    # حفظ تقرير HTML للتغطية
    try:
        cov.html_report(directory='htmlcov')
        print("تم حفظ تقرير التغطية في مجلد htmlcov/")
    except Exception as e:
        print(f"فشل في حفظ تقرير HTML: {e}")
    
    print()
    print("=" * 60)
    
    # إرجاع نتيجة النجاح
    return result.wasSuccessful()

def run_specific_test(test_name):
    """تشغيل اختبار محدد"""
    print(f"تشغيل الاختبار: {test_name}")
    print("-" * 40)
    
    # تحميل الاختبار المحدد
    test_loader = unittest.TestLoader()
    
    try:
        if test_name == "models":
            from tests.test_models import TestModels
            suite = test_loader.loadTestsFromTestCase(TestModels)
        elif test_name == "validators":
            from tests.test_functions import TestValidators
            suite = test_loader.loadTestsFromTestCase(TestValidators)
        elif test_name == "utils":
            from tests.test_functions import TestUtils
            suite = test_loader.loadTestsFromTestCase(TestUtils)
        elif test_name == "files":
            from tests.test_functions import TestFileOperations
            suite = test_loader.loadTestsFromTestCase(TestFileOperations)
        elif test_name == "database":
            from tests.test_functions import TestDatabaseOperations
            suite = test_loader.loadTestsFromTestCase(TestDatabaseOperations)
        else:
            print(f"اختبار غير معروف: {test_name}")
            return False
        
        # تشغيل الاختبار
        runner = unittest.TextTestRunner(verbosity=2)
        result = runner.run(suite)
        
        return result.wasSuccessful()
        
    except ImportError as e:
        print(f"فشل في تحميل الاختبار: {e}")
        return False

def create_test_report():
    """إنشاء تقرير مفصل للاختبارات"""
    print("إنشاء تقرير الاختبارات...")
    
    # تشغيل الاختبارات مع تسجيل النتائج
    cov = coverage.Coverage()
    cov.start()
    
    # تحميل جميع الاختبارات
    test_loader = unittest.TestLoader()
    test_suite = unittest.TestSuite()
    
    # قائمة الاختبارات
    test_classes = []
    
    try:
        from tests.test_models import TestModels
        test_classes.append(('اختبارات النماذج', TestModels))
    except ImportError:
        pass
    
    try:
        from tests.test_functions import TestValidators, TestUtils, TestFileOperations, TestDatabaseOperations
        test_classes.extend([
            ('اختبارات التحقق من صحة البيانات', TestValidators),
            ('اختبارات الدوال المساعدة', TestUtils),
            ('اختبارات عمليات الملفات', TestFileOperations),
            ('اختبارات عمليات قاعدة البيانات', TestDatabaseOperations)
        ])
    except ImportError:
        pass
    
    # تشغيل كل مجموعة اختبارات منفصلة
    report_data = []
    
    for test_name, test_class in test_classes:
        print(f"\nتشغيل {test_name}...")
        suite = test_loader.loadTestsFromTestCase(test_class)
        runner = unittest.TextTestRunner(verbosity=0, stream=open(os.devnull, 'w'))
        result = runner.run(suite)
        
        report_data.append({
            'name': test_name,
            'total': result.testsRun,
            'successful': result.testsRun - len(result.failures) - len(result.errors),
            'failures': len(result.failures),
            'errors': len(result.errors),
            'success_rate': ((result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100) if result.testsRun > 0 else 0
        })
    
    cov.stop()
    cov.save()
    
    # إنشاء تقرير HTML
    report_html = """
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
        <meta charset="UTF-8">
        <title>تقرير الاختبارات - نظام شؤون الموظفين</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            .header { background: #007bff; color: white; padding: 20px; border-radius: 5px; }
            .summary { margin: 20px 0; }
            .test-group { margin: 10px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
            .success { background-color: #d4edda; }
            .warning { background-color: #fff3cd; }
            .danger { background-color: #f8d7da; }
            .progress-bar { width: 100%; height: 20px; background: #f0f0f0; border-radius: 10px; overflow: hidden; }
            .progress-fill { height: 100%; background: #28a745; }
        </style>
    </head>
    <body>
        <div class="header">
            <h1>تقرير الاختبارات - نظام شؤون الموظفين</h1>
            <p>تاريخ التقرير: """ + datetime.now().strftime('%Y-%m-%d %H:%M:%S') + """</p>
        </div>
        
        <div class="summary">
            <h2>ملخص عام</h2>
    """
    
    total_tests = sum(data['total'] for data in report_data)
    total_successful = sum(data['successful'] for data in report_data)
    overall_success_rate = (total_successful / total_tests * 100) if total_tests > 0 else 0
    
    report_html += f"""
            <p>إجمالي الاختبارات: {total_tests}</p>
            <p>الاختبارات الناجحة: {total_successful}</p>
            <p>معدل النجاح الإجمالي: {overall_success_rate:.1f}%</p>
            <div class="progress-bar">
                <div class="progress-fill" style="width: {overall_success_rate}%;"></div>
            </div>
        </div>
        
        <h2>تفاصيل الاختبارات</h2>
    """
    
    for data in report_data:
        status_class = "success" if data['success_rate'] == 100 else "warning" if data['success_rate'] >= 80 else "danger"
        
        report_html += f"""
        <div class="test-group {status_class}">
            <h3>{data['name']}</h3>
            <p>إجمالي الاختبارات: {data['total']}</p>
            <p>نجح: {data['successful']}</p>
            <p>فشل: {data['failures']}</p>
            <p>أخطاء: {data['errors']}</p>
            <p>معدل النجاح: {data['success_rate']:.1f}%</p>
            <div class="progress-bar">
                <div class="progress-fill" style="width: {data['success_rate']}%;"></div>
            </div>
        </div>
        """
    
    report_html += """
        </body>
        </html>
    """
    
    # حفظ التقرير
    with open('test_report.html', 'w', encoding='utf-8') as f:
        f.write(report_html)
    
    print("تم حفظ التقرير في test_report.html")

if __name__ == '__main__':
    if len(sys.argv) > 1:
        command = sys.argv[1]
        
        if command == "all":
            success = run_all_tests()
            sys.exit(0 if success else 1)
        elif command == "report":
            create_test_report()
        elif command in ["models", "validators", "utils", "files", "database"]:
            success = run_specific_test(command)
            sys.exit(0 if success else 1)
        else:
            print("الاستخدام:")
            print("python run_tests.py all          # تشغيل جميع الاختبارات")
            print("python run_tests.py models       # اختبارات النماذج")
            print("python run_tests.py validators   # اختبارات التحقق")
            print("python run_tests.py utils        # اختبارات الدوال المساعدة")
            print("python run_tests.py files        # اختبارات الملفات")
            print("python run_tests.py database     # اختبارات قاعدة البيانات")
            print("python run_tests.py report       # إنشاء تقرير مفصل")
    else:
        # تشغيل جميع الاختبارات افتراضياً
        success = run_all_tests()
        sys.exit(0 if success else 1)
