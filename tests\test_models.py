"""
اختبارات النماذج (Models Tests)
"""

import unittest
from datetime import datetime, date
import sys
import os

# إضافة المجلد الجذر للمشروع إلى المسار
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app_init import create_app, db
from models import Employee, Department, Attendance, Leave, Document, Departure

class TestModels(unittest.TestCase):
    """اختبارات النماذج"""
    
    def setUp(self):
        """إعداد الاختبارات"""
        self.app = create_app(testing=True)
        self.app_context = self.app.app_context()
        self.app_context.push()
        
        # إنشاء قاعدة بيانات اختبار
        db.create_all()
        
        # إنشاء قسم للاختبار
        self.test_department = Department(
            name="قسم الاختبار",
            description="قسم للاختبارات"
        )
        db.session.add(self.test_department)
        db.session.commit()
    
    def tearDown(self):
        """تنظيف بعد الاختبارات"""
        db.session.remove()
        db.drop_all()
        self.app_context.pop()
    
    def test_department_creation(self):
        """اختبار إنشاء قسم"""
        department = Department(
            name="قسم تقنية المعلومات",
            description="قسم مسؤول عن تقنية المعلومات"
        )
        db.session.add(department)
        db.session.commit()
        
        self.assertIsNotNone(department.id)
        self.assertEqual(department.name, "قسم تقنية المعلومات")
        self.assertIsNotNone(department.created_at)
    
    def test_employee_creation(self):
        """اختبار إنشاء موظف"""
        employee = Employee(
            full_name="أحمد محمد علي",
            national_id="1234567890",
            birth_date=date(1990, 1, 1),
            gender="ذكر",
            nationality="سعودي",
            phone="0501234567",
            email="<EMAIL>",
            job_title="مطور برمجيات",
            department_id=self.test_department.id,
            hire_date=date.today(),
            salary=8000.00
        )
        db.session.add(employee)
        db.session.commit()
        
        self.assertIsNotNone(employee.id)
        self.assertEqual(employee.full_name, "أحمد محمد علي")
        self.assertEqual(employee.national_id, "1234567890")
        self.assertTrue(employee.is_active)
        self.assertIsNotNone(employee.created_at)
    
    def test_employee_department_relationship(self):
        """اختبار العلاقة بين الموظف والقسم"""
        employee = Employee(
            full_name="فاطمة أحمد",
            national_id="0987654321",
            birth_date=date(1992, 5, 15),
            gender="أنثى",
            nationality="سعودي",
            phone="0509876543",
            job_title="محاسبة",
            department_id=self.test_department.id,
            hire_date=date.today(),
            salary=6000.00
        )
        db.session.add(employee)
        db.session.commit()
        
        # اختبار العلاقة
        self.assertEqual(employee.department.name, "قسم الاختبار")
        self.assertIn(employee, self.test_department.employees)
    
    def test_attendance_creation(self):
        """اختبار إنشاء سجل حضور"""
        employee = Employee(
            full_name="محمد سالم",
            national_id="1122334455",
            birth_date=date(1988, 3, 10),
            gender="ذكر",
            nationality="سعودي",
            phone="0551122334",
            job_title="مهندس",
            department_id=self.test_department.id,
            hire_date=date.today(),
            salary=9000.00
        )
        db.session.add(employee)
        db.session.commit()
        
        attendance = Attendance(
            employee_id=employee.id,
            date=date.today(),
            check_in_time="08:00",
            check_out_time="17:00",
            status="حاضر"
        )
        db.session.add(attendance)
        db.session.commit()
        
        self.assertIsNotNone(attendance.id)
        self.assertEqual(attendance.employee_id, employee.id)
        self.assertEqual(attendance.status, "حاضر")
    
    def test_leave_creation(self):
        """اختبار إنشاء طلب إجازة"""
        employee = Employee(
            full_name="سارة خالد",
            national_id="5566778899",
            birth_date=date(1991, 7, 20),
            gender="أنثى",
            nationality="سعودي",
            phone="0555667788",
            job_title="مصممة",
            department_id=self.test_department.id,
            hire_date=date.today(),
            salary=7000.00
        )
        db.session.add(employee)
        db.session.commit()
        
        leave = Leave(
            employee_id=employee.id,
            leave_type="إجازة سنوية",
            start_date=date(2024, 6, 1),
            end_date=date(2024, 6, 5),
            days_count=5,
            reason="إجازة سنوية",
            status="معتمدة"
        )
        db.session.add(leave)
        db.session.commit()
        
        self.assertIsNotNone(leave.id)
        self.assertEqual(leave.employee_id, employee.id)
        self.assertEqual(leave.days_count, 5)
        self.assertEqual(leave.status, "معتمدة")
    
    def test_document_creation(self):
        """اختبار إنشاء وثيقة"""
        employee = Employee(
            full_name="عبدالله أحمد",
            national_id="9988776655",
            birth_date=date(1989, 11, 5),
            gender="ذكر",
            nationality="سعودي",
            phone="0559988776",
            job_title="محلل نظم",
            department_id=self.test_department.id,
            hire_date=date.today(),
            salary=8500.00
        )
        db.session.add(employee)
        db.session.commit()
        
        document = Document(
            employee_id=employee.id,
            document_type="صورة الهوية",
            file_name="id_copy.pdf",
            file_path="/uploads/documents/id_copy.pdf",
            file_size=1024000
        )
        db.session.add(document)
        db.session.commit()
        
        self.assertIsNotNone(document.id)
        self.assertEqual(document.employee_id, employee.id)
        self.assertEqual(document.document_type, "صورة الهوية")
        self.assertIsNotNone(document.created_at)
    
    def test_departure_creation(self):
        """اختبار إنشاء سجل مغادرة"""
        employee = Employee(
            full_name="نورا سعد",
            national_id="1357924680",
            birth_date=date(1993, 2, 14),
            gender="أنثى",
            nationality="سعودي",
            phone="0551357924",
            job_title="مترجمة",
            department_id=self.test_department.id,
            hire_date=date.today(),
            salary=5500.00
        )
        db.session.add(employee)
        db.session.commit()
        
        departure = Departure(
            employee_id=employee.id,
            departure_date=date.today(),
            departure_time="15:30",
            return_time="16:30",
            reason="موعد طبي",
            status="معتمدة"
        )
        db.session.add(departure)
        db.session.commit()
        
        self.assertIsNotNone(departure.id)
        self.assertEqual(departure.employee_id, employee.id)
        self.assertEqual(departure.reason, "موعد طبي")
        self.assertEqual(departure.status, "معتمدة")
    
    def test_employee_validation(self):
        """اختبار التحقق من صحة بيانات الموظف"""
        # اختبار رقم هوية مكرر
        employee1 = Employee(
            full_name="موظف أول",
            national_id="1111111111",
            birth_date=date(1990, 1, 1),
            gender="ذكر",
            nationality="سعودي",
            phone="0501111111",
            job_title="موظف",
            department_id=self.test_department.id,
            hire_date=date.today(),
            salary=5000.00
        )
        db.session.add(employee1)
        db.session.commit()
        
        employee2 = Employee(
            full_name="موظف ثاني",
            national_id="1111111111",  # نفس رقم الهوية
            birth_date=date(1991, 1, 1),
            gender="ذكر",
            nationality="سعودي",
            phone="0502222222",
            job_title="موظف",
            department_id=self.test_department.id,
            hire_date=date.today(),
            salary=5000.00
        )
        db.session.add(employee2)
        
        # يجب أن يفشل بسبب تكرار رقم الهوية
        with self.assertRaises(Exception):
            db.session.commit()
    
    def test_employee_age_calculation(self):
        """اختبار حساب عمر الموظف"""
        employee = Employee(
            full_name="خالد محمد",
            national_id="2468135790",
            birth_date=date(1985, 6, 15),
            gender="ذكر",
            nationality="سعودي",
            phone="0552468135",
            job_title="مدير",
            department_id=self.test_department.id,
            hire_date=date.today(),
            salary=12000.00
        )
        db.session.add(employee)
        db.session.commit()
        
        # حساب العمر المتوقع
        today = date.today()
        expected_age = today.year - 1985
        if today.month < 6 or (today.month == 6 and today.day < 15):
            expected_age -= 1
        
        self.assertEqual(employee.age, expected_age)
    
    def test_employee_years_of_service(self):
        """اختبار حساب سنوات الخدمة"""
        hire_date = date(2020, 1, 1)
        employee = Employee(
            full_name="علي حسن",
            national_id="9876543210",
            birth_date=date(1987, 4, 20),
            gender="ذكر",
            nationality="سعودي",
            phone="0559876543",
            job_title="مشرف",
            department_id=self.test_department.id,
            hire_date=hire_date,
            salary=10000.00
        )
        db.session.add(employee)
        db.session.commit()
        
        # حساب سنوات الخدمة المتوقعة
        today = date.today()
        expected_years = today.year - 2020
        if today.month < 1:
            expected_years -= 1
        
        self.assertEqual(employee.years_of_service, expected_years)

if __name__ == '__main__':
    unittest.main()
