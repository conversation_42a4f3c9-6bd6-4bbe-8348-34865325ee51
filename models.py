from flask_sqlalchemy import SQLAlchemy
from flask_login import UserMixin
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime

db = SQLAlchemy() # This will be initialized in app.py

# تعريف نموذج المستخدم لـ Flask-Login
class User(UserMixin, db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(128), nullable=False)
    role = db.Column(db.String(20), nullable=False, default='user')
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    is_active = db.Column(db.<PERSON>, default=True)

    def set_password(self, password):
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        return check_password_hash(self.password_hash, password)

    def __repr__(self):
        return f'<User {self.username}>'

# تعريف نموذج الموظف
class Employee(db.Model):
    __tablename__ = 'employee'

    id = db.Column(db.Integer, primary_key=True)

    # البيانات الشخصية
    full_name = db.Column(db.String(100), nullable=False, index=True)
    national_id = db.Column(db.String(20), unique=True, nullable=False, index=True)
    birth_place_municipality = db.Column(db.String(50))
    date_of_birth = db.Column(db.Date)
    gender = db.Column(db.String(10), default='ذكر')
    nationality = db.Column(db.String(50), default='سعودي')
    address = db.Column(db.String(200))
    phone_number = db.Column(db.String(20))
    email = db.Column(db.String(120), index=True)
    passport_number = db.Column(db.String(50))
    passport_expiry_date = db.Column(db.Date)
    notes = db.Column(db.Text)

    # البيانات الوظيفية
    employee_id = db.Column(db.String(20), unique=True, nullable=False, index=True)
    job_title = db.Column(db.String(100), index=True)
    appointment_type = db.Column(db.String(50), default='تعيين') # تعيين-عقد-ندب-تكليف
    appointment_date = db.Column(db.Date)
    start_work_date = db.Column(db.Date)
    current_grade = db.Column(db.String(20))
    allowances_count = db.Column(db.Integer, default=0)
    last_promotion_date = db.Column(db.Date)
    years_of_service = db.Column(db.Integer, default=0)
    photo = db.Column(db.String(100)) # مسار الصورة

    # العلاقات
    department_id = db.Column(db.Integer, db.ForeignKey('department.id'), nullable=True, index=True)
    department = db.relationship('Department', backref='employees', foreign_keys=[department_id])

    # طوابع زمنية
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # قيود إضافية
    __table_args__ = (
        db.CheckConstraint('length(national_id) = 10', name='check_national_id_length'),
        db.CheckConstraint("gender IN ('ذكر', 'أنثى')", name='check_gender'),
        db.CheckConstraint("appointment_type IN ('تعيين', 'عقد', 'ندب', 'تكليف')", name='check_appointment_type'),
        db.CheckConstraint('allowances_count >= 0', name='check_allowances_count'),
        db.CheckConstraint('years_of_service >= 0', name='check_years_of_service'),
    )

    def __repr__(self):
        return f'<Employee {self.full_name}>'

# تعريف نموذج القسم
class Department(db.Model):
    __tablename__ = 'department'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), unique=True, nullable=False, index=True)
    description = db.Column(db.String(255))
    creation_date = db.Column(db.DateTime, default=datetime.utcnow, index=True)
    manager_id = db.Column(db.Integer, db.ForeignKey('employee.id'), nullable=True, index=True)
    manager = db.relationship('Employee', foreign_keys=[manager_id])

    # طوابع زمنية
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # قيود إضافية
    __table_args__ = (
        db.CheckConstraint('length(name) >= 2', name='check_department_name_length'),
    )

    def __repr__(self):
        return f'<Department {self.name}>'

# تعريف نموذج الحضور
class Attendance(db.Model):
    __tablename__ = 'attendance'

    id = db.Column(db.Integer, primary_key=True)
    employee_id = db.Column(db.Integer, db.ForeignKey('employee.id'), nullable=False, index=True)
    date = db.Column(db.Date, nullable=False, index=True)
    status = db.Column(db.String(50), nullable=False, index=True) # حاضر, غائب, إجازة, مهمة, مرض
    check_in_time = db.Column(db.Time)
    check_out_time = db.Column(db.Time)
    notes = db.Column(db.Text)

    # العلاقات
    employee = db.relationship('Employee', backref='attendance')

    # طوابع زمنية
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # قيود إضافية
    __table_args__ = (
        db.UniqueConstraint('employee_id', 'date', name='unique_employee_date'),
        db.CheckConstraint("status IN ('حاضر', 'غائب', 'إجازة', 'مهمة', 'مرض')", name='check_attendance_status'),
        db.Index('idx_attendance_employee_date', 'employee_id', 'date'),
        db.Index('idx_attendance_date_status', 'date', 'status'),
    )

    def __repr__(self):
        return f'<Attendance {self.employee_id} - {self.date} - {self.status}>'

# تعريف نموذج الإجازات
class Leave(db.Model):
    __tablename__ = 'leave'

    id = db.Column(db.Integer, primary_key=True)
    employee_id = db.Column(db.Integer, db.ForeignKey('employee.id'), nullable=False, index=True)
    start_date = db.Column(db.Date, nullable=False, index=True)
    end_date = db.Column(db.Date, nullable=False, index=True)
    leave_type = db.Column(db.String(50), nullable=False, index=True) # سنوية, مرضية, طارئة, أمومة, حج, عارضة
    status = db.Column(db.String(50), nullable=False, default='معلقة', index=True) # معلقة, موافق عليها, مرفوضة
    reason = db.Column(db.Text)
    days_count = db.Column(db.Integer)
    approved_by = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=True)
    approved_at = db.Column(db.DateTime)

    # العلاقات
    employee = db.relationship('Employee', backref='leaves')
    approver = db.relationship('User', foreign_keys=[approved_by])

    # طوابع زمنية
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # قيود إضافية
    __table_args__ = (
        db.CheckConstraint('start_date <= end_date', name='check_leave_dates'),
        db.CheckConstraint("leave_type IN ('سنوية', 'مرضية', 'طارئة', 'أمومة', 'حج', 'عارضة')", name='check_leave_type'),
        db.CheckConstraint("status IN ('معلقة', 'موافق عليها', 'مرفوضة')", name='check_leave_status'),
        db.CheckConstraint('days_count > 0', name='check_days_count'),
        db.Index('idx_employee_dates', 'employee_id', 'start_date', 'end_date'),
        db.Index('idx_status_type', 'status', 'leave_type'),
    )

    def __repr__(self):
        return f'<Leave {self.employee_id} - {self.start_date} to {self.end_date}>'

# تعريف نموذج الوثائق
class Document(db.Model):
    __tablename__ = 'document'

    id = db.Column(db.Integer, primary_key=True)
    employee_id = db.Column(db.Integer, db.ForeignKey('employee.id'), nullable=False, index=True)
    document_type = db.Column(db.String(100), nullable=False, index=True)
    file_path = db.Column(db.String(255), nullable=False)
    original_filename = db.Column(db.String(255))
    file_size = db.Column(db.Integer) # بالبايت
    mime_type = db.Column(db.String(100))
    upload_date = db.Column(db.DateTime, default=datetime.utcnow, index=True)
    uploaded_by = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=True)

    # العلاقات
    employee = db.relationship('Employee', backref='documents')
    uploader = db.relationship('User', foreign_keys=[uploaded_by])

    # طوابع زمنية
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # قيود إضافية
    __table_args__ = (
        db.CheckConstraint('file_size > 0', name='check_file_size'),
        db.Index('idx_employee_type', 'employee_id', 'document_type'),
        db.Index('idx_upload_date', 'upload_date'),
    )

    def __repr__(self):
        return f'<Document {self.document_type} for Employee {self.employee_id}>'

# تعريف نموذج المغادرات (الخروج المبكر/التأخر)
class Departure(db.Model):
    __tablename__ = 'departure'

    id = db.Column(db.Integer, primary_key=True)
    employee_id = db.Column(db.Integer, db.ForeignKey('employee.id'), nullable=False, index=True)
    date = db.Column(db.Date, nullable=False, index=True)
    departure_time = db.Column(db.Time, nullable=False)
    return_time = db.Column(db.Time) # وقت العودة في حالة الخروج المؤقت
    reason = db.Column(db.Text)
    type = db.Column(db.String(50), default='خروج مبكر', index=True) # خروج مبكر, تأخر, خروج مؤقت
    status = db.Column(db.String(50), default='معلق') # معلق, موافق عليه, مرفوض
    approved_by = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=True)
    approved_at = db.Column(db.DateTime)

    # العلاقات
    employee = db.relationship('Employee', backref='departures')
    approver = db.relationship('User', foreign_keys=[approved_by])

    # طوابع زمنية
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # قيود إضافية
    __table_args__ = (
        db.CheckConstraint("type IN ('خروج مبكر', 'تأخر', 'خروج مؤقت')", name='check_departure_type'),
        db.CheckConstraint("status IN ('معلق', 'موافق عليه', 'مرفوض')", name='check_departure_status'),
        db.Index('idx_departure_employee_date', 'employee_id', 'date'),
        db.Index('idx_departure_date_type', 'date', 'type'),
    )

    def __repr__(self):
        return f'<Departure {self.employee_id} - {self.date} - {self.departure_time}>'