"""
نظام الأذونات والصلاحيات
"""

from functools import wraps
from flask import session, redirect, url_for, flash, request, abort
from flask_login import current_user
from enum import Enum

class Permission(Enum):
    """تعداد الأذونات المختلفة في النظام"""
    # أذونات الموظفين
    VIEW_EMPLOYEES = "view_employees"
    ADD_EMPLOYEE = "add_employee"
    EDIT_EMPLOYEE = "edit_employee"
    DELETE_EMPLOYEE = "delete_employee"
    
    # أذونات الأقسام
    VIEW_DEPARTMENTS = "view_departments"
    MANAGE_DEPARTMENTS = "manage_departments"
    
    # أذونات الحضور والانصراف
    VIEW_ATTENDANCE = "view_attendance"
    MANAGE_ATTENDANCE = "manage_attendance"
    
    # أذونات الإجازات
    VIEW_LEAVES = "view_leaves"
    MANAGE_LEAVES = "manage_leaves"
    APPROVE_LEAVES = "approve_leaves"
    
    # أذونات الوثائق
    VIEW_DOCUMENTS = "view_documents"
    MANAGE_DOCUMENTS = "manage_documents"
    
    # أذونات التقارير
    VIEW_REPORTS = "view_reports"
    EXPORT_DATA = "export_data"
    
    # أذونات النظام
    BACKUP_DATABASE = "backup_database"
    RESTORE_DATABASE = "restore_database"
    MANAGE_USERS = "manage_users"
    SYSTEM_SETTINGS = "system_settings"

class Role(Enum):
    """الأدوار المختلفة في النظام"""
    ADMIN = "admin"
    HR_MANAGER = "hr_manager"
    HR_EMPLOYEE = "hr_employee"
    DEPARTMENT_MANAGER = "department_manager"
    EMPLOYEE = "employee"
    VIEWER = "viewer"

# تعريف الأذونات لكل دور
ROLE_PERMISSIONS = {
    Role.ADMIN: [
        # جميع الأذونات
        Permission.VIEW_EMPLOYEES, Permission.ADD_EMPLOYEE, Permission.EDIT_EMPLOYEE, Permission.DELETE_EMPLOYEE,
        Permission.VIEW_DEPARTMENTS, Permission.MANAGE_DEPARTMENTS,
        Permission.VIEW_ATTENDANCE, Permission.MANAGE_ATTENDANCE,
        Permission.VIEW_LEAVES, Permission.MANAGE_LEAVES, Permission.APPROVE_LEAVES,
        Permission.VIEW_DOCUMENTS, Permission.MANAGE_DOCUMENTS,
        Permission.VIEW_REPORTS, Permission.EXPORT_DATA,
        Permission.BACKUP_DATABASE, Permission.RESTORE_DATABASE, Permission.MANAGE_USERS, Permission.SYSTEM_SETTINGS
    ],
    
    Role.HR_MANAGER: [
        # أذونات إدارة الموارد البشرية
        Permission.VIEW_EMPLOYEES, Permission.ADD_EMPLOYEE, Permission.EDIT_EMPLOYEE, Permission.DELETE_EMPLOYEE,
        Permission.VIEW_DEPARTMENTS, Permission.MANAGE_DEPARTMENTS,
        Permission.VIEW_ATTENDANCE, Permission.MANAGE_ATTENDANCE,
        Permission.VIEW_LEAVES, Permission.MANAGE_LEAVES, Permission.APPROVE_LEAVES,
        Permission.VIEW_DOCUMENTS, Permission.MANAGE_DOCUMENTS,
        Permission.VIEW_REPORTS, Permission.EXPORT_DATA
    ],
    
    Role.HR_EMPLOYEE: [
        # أذونات موظف الموارد البشرية
        Permission.VIEW_EMPLOYEES, Permission.ADD_EMPLOYEE, Permission.EDIT_EMPLOYEE,
        Permission.VIEW_DEPARTMENTS,
        Permission.VIEW_ATTENDANCE, Permission.MANAGE_ATTENDANCE,
        Permission.VIEW_LEAVES, Permission.MANAGE_LEAVES,
        Permission.VIEW_DOCUMENTS, Permission.MANAGE_DOCUMENTS,
        Permission.VIEW_REPORTS
    ],
    
    Role.DEPARTMENT_MANAGER: [
        # أذونات مدير القسم
        Permission.VIEW_EMPLOYEES, Permission.EDIT_EMPLOYEE,
        Permission.VIEW_DEPARTMENTS,
        Permission.VIEW_ATTENDANCE, Permission.MANAGE_ATTENDANCE,
        Permission.VIEW_LEAVES, Permission.APPROVE_LEAVES,
        Permission.VIEW_DOCUMENTS,
        Permission.VIEW_REPORTS
    ],
    
    Role.EMPLOYEE: [
        # أذونات الموظف العادي
        Permission.VIEW_EMPLOYEES,
        Permission.VIEW_DEPARTMENTS,
        Permission.VIEW_ATTENDANCE,
        Permission.VIEW_LEAVES,
        Permission.VIEW_DOCUMENTS
    ],
    
    Role.VIEWER: [
        # أذونات المشاهد فقط
        Permission.VIEW_EMPLOYEES,
        Permission.VIEW_DEPARTMENTS,
        Permission.VIEW_ATTENDANCE,
        Permission.VIEW_LEAVES,
        Permission.VIEW_DOCUMENTS,
        Permission.VIEW_REPORTS
    ]
}

def get_user_role():
    """الحصول على دور المستخدم الحالي"""
    # في حالة عدم وجود نظام مستخدمين متقدم، نعتبر الجميع مدراء
    return session.get('user_role', Role.ADMIN.value)

def set_user_role(role):
    """تعيين دور المستخدم"""
    if isinstance(role, Role):
        session['user_role'] = role.value
    else:
        session['user_role'] = role

def has_permission(permission):
    """التحقق من وجود صلاحية معينة للمستخدم الحالي"""
    user_role = get_user_role()
    
    # تحويل النص إلى enum إذا لزم الأمر
    if isinstance(user_role, str):
        try:
            user_role = Role(user_role)
        except ValueError:
            return False
    
    if isinstance(permission, str):
        try:
            permission = Permission(permission)
        except ValueError:
            return False
    
    # التحقق من الأذونات
    user_permissions = ROLE_PERMISSIONS.get(user_role, [])
    return permission in user_permissions

def require_permission(permission):
    """ديكوريتر للتحقق من الصلاحيات"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if not has_permission(permission):
                flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
                return redirect(url_for('main.dashboard'))
            return f(*args, **kwargs)
        return decorated_function
    return decorator

def require_any_permission(*permissions):
    """ديكوريتر للتحقق من وجود أي من الصلاحيات المحددة"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if not any(has_permission(perm) for perm in permissions):
                flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
                return redirect(url_for('main.dashboard'))
            return f(*args, **kwargs)
        return decorated_function
    return decorator

def require_all_permissions(*permissions):
    """ديكوريتر للتحقق من وجود جميع الصلاحيات المحددة"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if not all(has_permission(perm) for perm in permissions):
                flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
                return redirect(url_for('main.dashboard'))
            return f(*args, **kwargs)
        return decorated_function
    return decorator

def admin_required(f):
    """ديكوريتر يتطلب صلاحيات المدير"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if get_user_role() != Role.ADMIN.value:
            flash('هذه الصفحة متاحة للمدراء فقط', 'danger')
            return redirect(url_for('main.dashboard'))
        return f(*args, **kwargs)
    return decorated_function

def get_user_permissions():
    """الحصول على جميع صلاحيات المستخدم الحالي"""
    user_role = get_user_role()
    if isinstance(user_role, str):
        try:
            user_role = Role(user_role)
        except ValueError:
            return []
    
    return ROLE_PERMISSIONS.get(user_role, [])

def can_access_route(route_name):
    """التحقق من إمكانية الوصول إلى route معين"""
    # تعريف الصلاحيات المطلوبة لكل route
    route_permissions = {
        'main.view_employees': Permission.VIEW_EMPLOYEES,
        'main.add_employee': Permission.ADD_EMPLOYEE,
        'main.edit_employee': Permission.EDIT_EMPLOYEE,
        'main.delete_employee': Permission.DELETE_EMPLOYEE,
        'main.manage_departments': Permission.MANAGE_DEPARTMENTS,
        'main.manage_attendance': Permission.MANAGE_ATTENDANCE,
        'main.manage_leaves': Permission.MANAGE_LEAVES,
        'main.manage_documents': Permission.MANAGE_DOCUMENTS,
        'main.backup_database': Permission.BACKUP_DATABASE,
        'main.restore_database': Permission.RESTORE_DATABASE,
        'main.export_employees': Permission.EXPORT_DATA,
        'main.import_employees': Permission.MANAGE_EMPLOYEES,
    }
    
    required_permission = route_permissions.get(route_name)
    if required_permission:
        return has_permission(required_permission)
    
    return True  # السماح بالوصول إذا لم تكن هناك صلاحية محددة

def filter_menu_items(menu_items):
    """تصفية عناصر القائمة بناءً على صلاحيات المستخدم"""
    filtered_items = []
    
    for item in menu_items:
        if 'permission' in item:
            if has_permission(item['permission']):
                filtered_items.append(item)
        elif 'route' in item:
            if can_access_route(item['route']):
                filtered_items.append(item)
        else:
            filtered_items.append(item)
    
    return filtered_items

# دالة مساعدة لاستخدامها في القوالب
def template_has_permission(permission):
    """دالة للاستخدام في القوالب للتحقق من الصلاحيات"""
    return has_permission(permission)

# تسجيل الدالة في Jinja2
def register_permission_functions(app):
    """تسجيل دوال الصلاحيات في Jinja2"""
    app.jinja_env.globals['has_permission'] = template_has_permission
    app.jinja_env.globals['Permission'] = Permission
    app.jinja_env.globals['get_user_role'] = get_user_role
    app.jinja_env.globals['can_access_route'] = can_access_route
