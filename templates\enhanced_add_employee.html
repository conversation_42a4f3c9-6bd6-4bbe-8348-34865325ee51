{% extends "base.html" %}

{% block title %}إضافة موظف جديد - نظام شؤون الموظفين{% endblock %}

{% block page_title %}إضافة موظف جديد{% endblock %}

{% block head %}
<style>
    .form-wizard {
        background: white;
        border-radius: 15px;
        box-shadow: 0 8px 30px rgba(0,0,0,0.1);
        overflow: hidden;
    }
    
    .wizard-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 25px;
        text-align: center;
    }
    
    .wizard-steps {
        display: flex;
        justify-content: center;
        padding: 20px;
        background: #f8f9fa;
        border-bottom: 1px solid #e9ecef;
    }
    
    .step {
        display: flex;
        align-items: center;
        margin: 0 15px;
        color: #6c757d;
        font-weight: 500;
    }
    
    .step.active {
        color: #667eea;
    }
    
    .step.completed {
        color: #28a745;
    }
    
    .step-number {
        width: 30px;
        height: 30px;
        border-radius: 50%;
        background: #e9ecef;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-left: 10px;
        font-weight: 600;
        font-size: 14px;
    }
    
    .step.active .step-number {
        background: #667eea;
        color: white;
    }
    
    .step.completed .step-number {
        background: #28a745;
        color: white;
    }
    
    .form-section {
        padding: 30px;
    }
    
    .section-title {
        color: #495057;
        font-weight: 600;
        margin-bottom: 25px;
        padding-bottom: 10px;
        border-bottom: 2px solid #e9ecef;
    }
    
    .form-row {
        display: flex;
        gap: 20px;
        margin-bottom: 20px;
    }
    
    .form-col {
        flex: 1;
    }
    
    .required-field::after {
        content: " *";
        color: #dc3545;
        font-weight: bold;
    }
    
    .photo-upload {
        text-align: center;
        padding: 30px;
        border: 2px dashed #dee2e6;
        border-radius: 10px;
        background: #f8f9fa;
        transition: all 0.3s ease;
        cursor: pointer;
    }
    
    .photo-upload:hover {
        border-color: #667eea;
        background: #f0f4ff;
    }
    
    .photo-preview {
        width: 150px;
        height: 150px;
        border-radius: 10px;
        object-fit: cover;
        margin: 0 auto 15px;
        display: block;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }
    
    .upload-icon {
        font-size: 3rem;
        color: #6c757d;
        margin-bottom: 15px;
    }
    
    @media (max-width: 768px) {
        .form-row {
            flex-direction: column;
            gap: 0;
        }
        
        .wizard-steps {
            flex-wrap: wrap;
            gap: 10px;
        }
        
        .step {
            margin: 5px;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="form-wizard fade-in">
                <div class="wizard-header">
                    <h2><i class="fas fa-user-plus me-3"></i>إضافة موظف جديد</h2>
                    <p class="mb-0">يرجى ملء جميع البيانات المطلوبة بدقة</p>
                </div>
                
                <div class="wizard-steps">
                    <div class="step active">
                        <div class="step-number">1</div>
                        <span>البيانات الأساسية</span>
                    </div>
                    <div class="step">
                        <div class="step-number">2</div>
                        <span>معلومات الاتصال</span>
                    </div>
                    <div class="step">
                        <div class="step-number">3</div>
                        <span>بيانات العمل</span>
                    </div>
                    <div class="step">
                        <div class="step-number">4</div>
                        <span>الصورة الشخصية</span>
                    </div>
                </div>
                
                <form method="POST" enctype="multipart/form-data" data-validate="true" class="needs-validation" novalidate>
                    {{ csrf_token() }}
                    
                    <!-- البيانات الأساسية -->
                    <div class="form-section">
                        <h4 class="section-title">
                            <i class="fas fa-user me-2"></i>البيانات الأساسية
                        </h4>
                        
                        <div class="form-row">
                            <div class="form-col">
                                <label for="full_name" class="form-label required-field">الاسم الكامل</label>
                                <input type="text" class="form-control" id="full_name" name="full_name" required>
                                <div class="invalid-feedback">يرجى إدخال الاسم الكامل</div>
                            </div>
                            <div class="form-col">
                                <label for="national_id" class="form-label required-field">رقم الهوية</label>
                                <input type="text" class="form-control" id="national_id" name="national_id" pattern="[0-9]{10}" required>
                                <div class="invalid-feedback">يرجى إدخال رقم هوية صحيح (10 أرقام)</div>
                            </div>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-col">
                                <label for="birth_date" class="form-label required-field">تاريخ الميلاد</label>
                                <input type="date" class="form-control" id="birth_date" name="birth_date" required>
                                <div class="invalid-feedback">يرجى إدخال تاريخ الميلاد</div>
                            </div>
                            <div class="form-col">
                                <label for="gender" class="form-label required-field">الجنس</label>
                                <select class="form-select" id="gender" name="gender" required>
                                    <option value="">اختر الجنس</option>
                                    <option value="ذكر">ذكر</option>
                                    <option value="أنثى">أنثى</option>
                                </select>
                                <div class="invalid-feedback">يرجى اختيار الجنس</div>
                            </div>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-col">
                                <label for="nationality" class="form-label required-field">الجنسية</label>
                                <input type="text" class="form-control" id="nationality" name="nationality" value="سعودي" required>
                                <div class="invalid-feedback">يرجى إدخال الجنسية</div>
                            </div>
                            <div class="form-col">
                                <label for="marital_status" class="form-label">الحالة الاجتماعية</label>
                                <select class="form-select" id="marital_status" name="marital_status">
                                    <option value="">اختر الحالة الاجتماعية</option>
                                    <option value="أعزب">أعزب</option>
                                    <option value="متزوج">متزوج</option>
                                    <option value="مطلق">مطلق</option>
                                    <option value="أرمل">أرمل</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <!-- معلومات الاتصال -->
                    <div class="form-section">
                        <h4 class="section-title">
                            <i class="fas fa-address-book me-2"></i>معلومات الاتصال
                        </h4>
                        
                        <div class="form-row">
                            <div class="form-col">
                                <label for="phone" class="form-label required-field">رقم الهاتف</label>
                                <input type="tel" class="form-control" id="phone" name="phone" pattern="05[0-9]{8}" required>
                                <div class="invalid-feedback">يرجى إدخال رقم هاتف صحيح (يبدأ بـ 05)</div>
                            </div>
                            <div class="form-col">
                                <label for="email" class="form-label">البريد الإلكتروني</label>
                                <input type="email" class="form-control" id="email" name="email">
                                <div class="invalid-feedback">يرجى إدخال بريد إلكتروني صحيح</div>
                            </div>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-col">
                                <label for="address" class="form-label">العنوان</label>
                                <textarea class="form-control" id="address" name="address" rows="3"></textarea>
                            </div>
                        </div>
                    </div>
                    
                    <!-- بيانات العمل -->
                    <div class="form-section">
                        <h4 class="section-title">
                            <i class="fas fa-briefcase me-2"></i>بيانات العمل
                        </h4>
                        
                        <div class="form-row">
                            <div class="form-col">
                                <label for="job_title" class="form-label required-field">المسمى الوظيفي</label>
                                <input type="text" class="form-control" id="job_title" name="job_title" required>
                                <div class="invalid-feedback">يرجى إدخال المسمى الوظيفي</div>
                            </div>
                            <div class="form-col">
                                <label for="department_id" class="form-label required-field">القسم</label>
                                <select class="form-select" id="department_id" name="department_id" required>
                                    <option value="">اختر القسم</option>
                                    {% for department in departments %}
                                        <option value="{{ department.id }}">{{ department.name }}</option>
                                    {% endfor %}
                                </select>
                                <div class="invalid-feedback">يرجى اختيار القسم</div>
                            </div>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-col">
                                <label for="hire_date" class="form-label required-field">تاريخ التوظيف</label>
                                <input type="date" class="form-control" id="hire_date" name="hire_date" required>
                                <div class="invalid-feedback">يرجى إدخال تاريخ التوظيف</div>
                            </div>
                            <div class="form-col">
                                <label for="salary" class="form-label required-field">الراتب</label>
                                <input type="number" class="form-control" id="salary" name="salary" min="0" step="0.01" required>
                                <div class="invalid-feedback">يرجى إدخال الراتب</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- الصورة الشخصية -->
                    <div class="form-section">
                        <h4 class="section-title">
                            <i class="fas fa-camera me-2"></i>الصورة الشخصية
                        </h4>
                        
                        <div class="photo-upload" onclick="document.getElementById('photo').click()">
                            <img id="photo-preview" class="photo-preview" style="display: none;">
                            <div id="upload-placeholder">
                                <i class="fas fa-cloud-upload-alt upload-icon"></i>
                                <h5>اضغط لرفع الصورة الشخصية</h5>
                                <p class="text-muted">JPG, PNG أو GIF (الحد الأقصى 5MB)</p>
                            </div>
                            <input type="file" id="photo" name="photo" accept="image/*" style="display: none;">
                        </div>
                    </div>
                    
                    <div class="form-section">
                        <div class="d-flex justify-content-between">
                            <a href="{{ url_for('main.view_employees') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-right me-2"></i>إلغاء
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>حفظ الموظف
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // معاينة الصورة
    document.getElementById('photo').addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                const preview = document.getElementById('photo-preview');
                const placeholder = document.getElementById('upload-placeholder');
                
                preview.src = e.target.result;
                preview.style.display = 'block';
                placeholder.style.display = 'none';
            };
            reader.readAsDataURL(file);
        }
    });
    
    // تحديث خطوات المعالج عند التمرير
    window.addEventListener('scroll', function() {
        const sections = document.querySelectorAll('.form-section');
        const steps = document.querySelectorAll('.step');
        
        let currentSection = 0;
        sections.forEach((section, index) => {
            const rect = section.getBoundingClientRect();
            if (rect.top <= 200) {
                currentSection = index;
            }
        });
        
        steps.forEach((step, index) => {
            step.classList.remove('active', 'completed');
            if (index === currentSection) {
                step.classList.add('active');
            } else if (index < currentSection) {
                step.classList.add('completed');
            }
        });
    });
</script>
{% endblock %}
