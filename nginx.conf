events {
    worker_connections 1024;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    # إعدادات الضغط
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;

    # إعدادات الأمان
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;

    # إعدادات التخزين المؤقت
    location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # حد حجم الملف المرفوع
    client_max_body_size 10M;

    # إعدادات الخادم
    upstream employee_app {
        server employee-system:5000;
    }

    # إعادة توجيه HTTP إلى HTTPS
    server {
        listen 80;
        server_name _;
        return 301 https://$server_name$request_uri;
    }

    # خادم HTTPS الرئيسي
    server {
        listen 443 ssl http2;
        server_name _;

        # شهادات SSL
        ssl_certificate /etc/nginx/ssl/cert.pem;
        ssl_certificate_key /etc/nginx/ssl/key.pem;
        
        # إعدادات SSL الآمنة
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
        ssl_prefer_server_ciphers off;
        ssl_session_cache shared:SSL:10m;
        ssl_session_timeout 10m;

        # الملفات الثابتة
        location /static/ {
            alias /var/www/static/;
            expires 1y;
            add_header Cache-Control "public, immutable";
        }

        # التطبيق الرئيسي
        location / {
            proxy_pass http://employee_app;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # إعدادات المهلة الزمنية
            proxy_connect_timeout 60s;
            proxy_send_timeout 60s;
            proxy_read_timeout 60s;
            
            # إعدادات التخزين المؤقت
            proxy_buffering on;
            proxy_buffer_size 128k;
            proxy_buffers 4 256k;
            proxy_busy_buffers_size 256k;
        }

        # صفحة الصحة
        location /health {
            access_log off;
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }

        # منع الوصول للملفات الحساسة
        location ~ /\. {
            deny all;
        }

        location ~ \.(env|log|ini)$ {
            deny all;
        }

        # إعدادات السجلات
        access_log /var/log/nginx/employee_access.log;
        error_log /var/log/nginx/employee_error.log;
    }

    # خادم للملفات الثابتة فقط (اختياري)
    server {
        listen 8081;
        server_name _;

        location / {
            root /var/www/static;
            try_files $uri $uri/ =404;
        }

        # منع الوصول للملفات الحساسة
        location ~ /\. {
            deny all;
        }
    }
}
