version: '3.8'

services:
  # تطبيق نظام شؤون الموظفين
  employee-system:
    build: .
    container_name: employee_management_system
    ports:
      - "5000:5000"
    environment:
      - FLASK_ENV=production
      - SECRET_KEY=${SECRET_KEY:-your-secret-key-change-this}
      - DATABASE_URL=sqlite:///employees.db
      - REDIS_URL=redis://redis:6379/0
    volumes:
      - ./data:/app/data
      - ./static/uploads:/app/static/uploads
      - ./logs:/app/logs
    depends_on:
      - redis
    restart: unless-stopped
    networks:
      - employee_network

  # Redis للتخزين المؤقت
  redis:
    image: redis:7-alpine
    container_name: employee_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    networks:
      - employee_network
    command: redis-server --appendonly yes

  # Nginx كخادم ويب عكسي (اختياري)
  nginx:
    image: nginx:alpine
    container_name: employee_nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./static:/var/www/static:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - employee-system
    restart: unless-stopped
    networks:
      - employee_network

  # PostgreSQL (بديل لـ SQLite في الإنتاج)
  postgres:
    image: postgres:15-alpine
    container_name: employee_postgres
    environment:
      - POSTGRES_DB=employees
      - POSTGRES_USER=employee_user
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-change-this-password}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    restart: unless-stopped
    networks:
      - employee_network
    profiles:
      - postgres

  # pgAdmin لإدارة PostgreSQL
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: employee_pgadmin
    environment:
      - PGADMIN_DEFAULT_EMAIL=<EMAIL>
      - PGADMIN_DEFAULT_PASSWORD=${PGADMIN_PASSWORD:-admin123}
    ports:
      - "8080:80"
    depends_on:
      - postgres
    restart: unless-stopped
    networks:
      - employee_network
    profiles:
      - postgres

volumes:
  redis_data:
    driver: local
  postgres_data:
    driver: local

networks:
  employee_network:
    driver: bridge
