"""
اختبارات الدوال (Functions Tests)
"""

import unittest
import os
import tempfile
from datetime import datetime, date
import sys

# إضافة المجلد الجذر للمشروع إلى المسار
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app_init import create_app, db
from models import Employee, Department
from validators import (
    validate_saudi_national_id, validate_phone_number, validate_email,
    validate_required_field, validate_date_range, validate_file_upload,
    sanitize_input, validate_salary
)
from utils import (
    format_currency, format_date_arabic, calculate_age,
    calculate_years_of_service, generate_employee_id,
    format_phone_number, validate_arabic_text
)

class TestValidators(unittest.TestCase):
    """اختبارات دوال التحقق من صحة البيانات"""
    
    def test_validate_saudi_national_id(self):
        """اختبار التحقق من رقم الهوية السعودي"""
        # أرقام هوية صحيحة
        valid_ids = ["1234567890", "2345678901", "3456789012"]
        for national_id in valid_ids:
            self.assertTrue(validate_saudi_national_id(national_id))
        
        # أرقام هوية غير صحيحة
        invalid_ids = ["123456789", "12345678901", "abcd567890", "", None]
        for national_id in invalid_ids:
            self.assertFalse(validate_saudi_national_id(national_id))
    
    def test_validate_phone_number(self):
        """اختبار التحقق من رقم الهاتف"""
        # أرقام هاتف صحيحة
        valid_phones = ["0501234567", "0551234567", "0561234567"]
        for phone in valid_phones:
            self.assertTrue(validate_phone_number(phone))
        
        # أرقام هاتف غير صحيحة
        invalid_phones = ["050123456", "05012345678", "1234567890", "", None]
        for phone in invalid_phones:
            self.assertFalse(validate_phone_number(phone))
    
    def test_validate_email(self):
        """اختبار التحقق من البريد الإلكتروني"""
        # بريد إلكتروني صحيح
        valid_emails = ["<EMAIL>", "<EMAIL>", "<EMAIL>"]
        for email in valid_emails:
            self.assertTrue(validate_email(email))
        
        # بريد إلكتروني غير صحيح
        invalid_emails = ["invalid-email", "@domain.com", "user@", "", None]
        for email in invalid_emails:
            self.assertFalse(validate_email(email))
    
    def test_validate_required_field(self):
        """اختبار التحقق من الحقول المطلوبة"""
        # قيم صحيحة
        valid_values = ["text", "123", "value"]
        for value in valid_values:
            self.assertTrue(validate_required_field(value))
        
        # قيم غير صحيحة
        invalid_values = ["", "   ", None]
        for value in invalid_values:
            self.assertFalse(validate_required_field(value))
    
    def test_validate_date_range(self):
        """اختبار التحقق من نطاق التاريخ"""
        start_date = date(2024, 1, 1)
        end_date = date(2024, 12, 31)
        
        # تواريخ صحيحة
        valid_dates = [date(2024, 6, 15), date(2024, 1, 1), date(2024, 12, 31)]
        for test_date in valid_dates:
            self.assertTrue(validate_date_range(test_date, start_date, end_date))
        
        # تواريخ غير صحيحة
        invalid_dates = [date(2023, 12, 31), date(2025, 1, 1)]
        for test_date in invalid_dates:
            self.assertFalse(validate_date_range(test_date, start_date, end_date))
    
    def test_validate_salary(self):
        """اختبار التحقق من الراتب"""
        # رواتب صحيحة
        valid_salaries = [5000, 10000.50, 15000]
        for salary in valid_salaries:
            self.assertTrue(validate_salary(salary))
        
        # رواتب غير صحيحة
        invalid_salaries = [-1000, 0, "abc", None]
        for salary in invalid_salaries:
            self.assertFalse(validate_salary(salary))
    
    def test_sanitize_input(self):
        """اختبار تنظيف المدخلات"""
        # نص يحتوي على HTML
        dirty_input = "<script>alert('xss')</script>Hello World"
        clean_input = sanitize_input(dirty_input)
        self.assertNotIn("<script>", clean_input)
        self.assertIn("Hello World", clean_input)
        
        # نص عادي
        normal_input = "نص عربي عادي"
        clean_input = sanitize_input(normal_input)
        self.assertEqual(normal_input, clean_input)

class TestUtils(unittest.TestCase):
    """اختبارات الدوال المساعدة"""
    
    def test_format_currency(self):
        """اختبار تنسيق العملة"""
        self.assertEqual(format_currency(1000), "1,000.00 ريال")
        self.assertEqual(format_currency(1500.50), "1,500.50 ريال")
        self.assertEqual(format_currency(0), "0.00 ريال")
    
    def test_format_date_arabic(self):
        """اختبار تنسيق التاريخ بالعربية"""
        test_date = date(2024, 6, 15)
        formatted_date = format_date_arabic(test_date)
        self.assertIn("2024", formatted_date)
        self.assertIn("يونيو", formatted_date)
        self.assertIn("15", formatted_date)
    
    def test_calculate_age(self):
        """اختبار حساب العمر"""
        birth_date = date(1990, 6, 15)
        age = calculate_age(birth_date)
        
        # حساب العمر المتوقع
        today = date.today()
        expected_age = today.year - 1990
        if today.month < 6 or (today.month == 6 and today.day < 15):
            expected_age -= 1
        
        self.assertEqual(age, expected_age)
    
    def test_calculate_years_of_service(self):
        """اختبار حساب سنوات الخدمة"""
        hire_date = date(2020, 1, 1)
        years = calculate_years_of_service(hire_date)
        
        # حساب سنوات الخدمة المتوقعة
        today = date.today()
        expected_years = today.year - 2020
        if today.month < 1:
            expected_years -= 1
        
        self.assertEqual(years, expected_years)
    
    def test_generate_employee_id(self):
        """اختبار توليد رقم الموظف"""
        employee_id = generate_employee_id()
        self.assertIsInstance(employee_id, str)
        self.assertTrue(len(employee_id) > 0)
        
        # التأكد من أن كل رقم مولد فريد
        ids = [generate_employee_id() for _ in range(10)]
        self.assertEqual(len(ids), len(set(ids)))
    
    def test_format_phone_number(self):
        """اختبار تنسيق رقم الهاتف"""
        self.assertEqual(format_phone_number("0501234567"), "************")
        self.assertEqual(format_phone_number("0551234567"), "************")
        self.assertEqual(format_phone_number("invalid"), "invalid")
    
    def test_validate_arabic_text(self):
        """اختبار التحقق من النص العربي"""
        # نصوص عربية صحيحة
        valid_texts = ["أحمد محمد", "فاطمة الزهراء", "عبدالله بن سعد"]
        for text in valid_texts:
            self.assertTrue(validate_arabic_text(text))
        
        # نصوص غير عربية
        invalid_texts = ["Ahmed Mohamed", "123456", "Test@#$"]
        for text in invalid_texts:
            self.assertFalse(validate_arabic_text(text))

class TestFileOperations(unittest.TestCase):
    """اختبارات عمليات الملفات"""
    
    def setUp(self):
        """إعداد الاختبارات"""
        self.test_dir = tempfile.mkdtemp()
    
    def tearDown(self):
        """تنظيف بعد الاختبارات"""
        import shutil
        shutil.rmtree(self.test_dir, ignore_errors=True)
    
    def test_validate_file_upload(self):
        """اختبار التحقق من رفع الملفات"""
        # إنشاء ملف اختبار
        test_file_path = os.path.join(self.test_dir, "test.pdf")
        with open(test_file_path, "wb") as f:
            f.write(b"PDF content")
        
        # اختبار ملف صحيح
        with open(test_file_path, "rb") as f:
            file_data = f.read()
            result = validate_file_upload(
                filename="test.pdf",
                file_data=file_data,
                allowed_extensions=['pdf', 'jpg', 'png'],
                max_size=1024*1024  # 1MB
            )
            self.assertTrue(result['valid'])
        
        # اختبار ملف بامتداد غير مسموح
        result = validate_file_upload(
            filename="test.exe",
            file_data=b"content",
            allowed_extensions=['pdf', 'jpg', 'png'],
            max_size=1024*1024
        )
        self.assertFalse(result['valid'])
        self.assertIn("امتداد الملف غير مسموح", result['error'])

class TestDatabaseOperations(unittest.TestCase):
    """اختبارات عمليات قاعدة البيانات"""
    
    def setUp(self):
        """إعداد الاختبارات"""
        self.app = create_app(testing=True)
        self.app_context = self.app.app_context()
        self.app_context.push()
        db.create_all()
        
        # إنشاء قسم للاختبار
        self.test_department = Department(
            name="قسم الاختبار",
            description="قسم للاختبارات"
        )
        db.session.add(self.test_department)
        db.session.commit()
    
    def tearDown(self):
        """تنظيف بعد الاختبارات"""
        db.session.remove()
        db.drop_all()
        self.app_context.pop()
    
    def test_employee_creation_with_validation(self):
        """اختبار إنشاء موظف مع التحقق من صحة البيانات"""
        from app_functions import create_employee_with_validation
        
        employee_data = {
            'full_name': 'محمد أحمد علي',
            'national_id': '1234567890',
            'birth_date': date(1990, 1, 1),
            'gender': 'ذكر',
            'nationality': 'سعودي',
            'phone': '0501234567',
            'email': '<EMAIL>',
            'job_title': 'مطور',
            'department_id': self.test_department.id,
            'hire_date': date.today(),
            'salary': 8000.00
        }
        
        result = create_employee_with_validation(employee_data)
        self.assertTrue(result['success'])
        self.assertIsNotNone(result['employee'])
    
    def test_employee_search(self):
        """اختبار البحث في الموظفين"""
        # إنشاء موظفين للاختبار
        employees_data = [
            {
                'full_name': 'أحمد محمد',
                'national_id': '1111111111',
                'birth_date': date(1990, 1, 1),
                'gender': 'ذكر',
                'nationality': 'سعودي',
                'phone': '0501111111',
                'job_title': 'مطور',
                'department_id': self.test_department.id,
                'hire_date': date.today(),
                'salary': 7000.00
            },
            {
                'full_name': 'فاطمة أحمد',
                'national_id': '2222222222',
                'birth_date': date(1992, 5, 15),
                'gender': 'أنثى',
                'nationality': 'سعودي',
                'phone': '0502222222',
                'job_title': 'محاسبة',
                'department_id': self.test_department.id,
                'hire_date': date.today(),
                'salary': 6000.00
            }
        ]
        
        for data in employees_data:
            employee = Employee(**data)
            db.session.add(employee)
        db.session.commit()
        
        # اختبار البحث بالاسم
        results = Employee.query.filter(Employee.full_name.like('%أحمد%')).all()
        self.assertEqual(len(results), 2)  # أحمد محمد و فاطمة أحمد
        
        # اختبار البحث بالمسمى الوظيفي
        results = Employee.query.filter(Employee.job_title == 'مطور').all()
        self.assertEqual(len(results), 1)
        self.assertEqual(results[0].full_name, 'أحمد محمد')

if __name__ == '__main__':
    # تشغيل جميع الاختبارات
    unittest.main(verbosity=2)
